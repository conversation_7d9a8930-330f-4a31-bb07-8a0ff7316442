import { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  Switch,
  FormControlLabel,
  IconButton,

  Paper,
  MenuItem,
  Button,
  Divider,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  Add as AddIcon,
  Remove as RemoveIcon,

  Person as PersonIcon,
  ChildCare as ChildIcon,
  People as PeopleIcon,
} from '@mui/icons-material';
import { useSharedData } from '../../../contexts/SharedDataContext';
import ResidentAutocomplete from '../../../components/common/ResidentAutocomplete';
import ComprehensiveFraudChecker from '../../../components/ComprehensiveFraudChecker';
import { useAuth } from '../../../contexts/AuthContext';

const FamilyInfoStep = ({
  formik,
  loading,
  addChild,
  removeChild
}) => {
  const { maritalStatuses } = useSharedData();
  const { user } = useAuth();

  // Prepare parent data for comprehensive fraud detection
  const prepareParentData = () => {
    const parents = [];

    // Add father information if available
    if (formik.values.father_first_name) {
      parents.push({
        relationship: 'father',
        first_name: formik.values.father_first_name,
        middle_name: formik.values.father_middle_name,
        last_name: formik.values.father_last_name
      });
    }

    // Add mother information if available
    if (formik.values.mother_first_name) {
      parents.push({
        relationship: 'mother',
        first_name: formik.values.mother_first_name,
        middle_name: formik.values.mother_middle_name,
        last_name: formik.values.mother_last_name
      });
    }

    return parents;
  };

  // Prepare citizen data for fraud detection
  const citizenData = {
    first_name: formik.values.first_name,
    middle_name: formik.values.middle_name,
    last_name: formik.values.last_name,
    date_of_birth: formik.values.date_of_birth,
    photo: formik.values.photo
  };

  const parentData = prepareParentData();

  // Check if citizen is single - find the "Single" marital status from shared data
  const getSingleMaritalStatusId = () => {
    if (!maritalStatuses) return null;
    const singleStatus = maritalStatuses.find(status =>
      status.name?.toLowerCase() === 'single'
    );
    return singleStatus?.id;
  };

  const singleStatusId = getSingleMaritalStatusId();
  const isSingle = formik.values.marital_status === singleStatusId ||
                   formik.values.marital_status === "" ||
                   !formik.values.marital_status;

  // Removed old search functionality - now using ResidentAutocomplete component
  // Removed expandable sections and old search functionality

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box>
        {/* Comprehensive Fraud Detection - Shows when parent data is available */}
        {parentData.length > 0 && (
          <ComprehensiveFraudChecker
            citizenData={citizenData}
            parentData={parentData}
            tenantId={user?.tenant_id}
            autoCheck={true}
            showDetails={true}
          />
        )}
        {/* Spouse Information - Only show if not single */}
        {isSingle && (
          <Card sx={{ mb: 3, bgcolor: 'grey.50' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 2 }}>
                <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body1" color="text.secondary">
                  Spouse information is not applicable for single citizens
                </Typography>
              </Box>
            </CardContent>
          </Card>
        )}

        {!isSingle && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6" component="h2">
                  Spouse Information
                </Typography>
              </Box>

            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formik.values.has_spouse}
                      onChange={(e) => {
                        formik.setFieldValue('has_spouse', e.target.checked);
                        if (!e.target.checked) {
                          formik.setFieldValue('is_spouse_resident', false);
                          formik.setFieldValue('spouse_id', '');
                          formik.setFieldValue('spouse_first_name', '');
                          formik.setFieldValue('spouse_middle_name', '');
                          formik.setFieldValue('spouse_last_name', '');
                          formik.setFieldValue('spouse_phone', '');
                          formik.setFieldValue('spouse_email', '');
                        }
                      }}
                      color="primary"
                    />
                  }
                  label="Citizen has a spouse"
                />
              </Grid>
            </Grid>

            {formik.values.has_spouse && (
                <Box>
                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formik.values.is_spouse_resident}
                            onChange={(e) => {
                              formik.setFieldValue('is_spouse_resident', e.target.checked);
                              if (!e.target.checked) {
                                formik.setFieldValue('spouse_id', '');
                              }
                            }}
                            color="primary"
                            disabled={!formik.values.has_spouse}
                          />
                        }
                        label="Spouse is a resident of this kebele"
                      />
                    </Grid>
                  </Grid>

                  {formik.values.is_spouse_resident ? (
                    <Box sx={{ mb: 2 }}>
                      <ResidentAutocomplete
                        label="Search for Spouse"
                        placeholder="Type to search for spouse..."
                        gender={formik.values.gender === 'male' ? 'female' : formik.values.gender === 'female' ? 'male' : null}
                        value={formik.values.spouse_id ? {
                          id: formik.values.spouse_id, // This is the database ID
                          first_name: formik.values.spouse_first_name,
                          middle_name: formik.values.spouse_middle_name,
                          last_name: formik.values.spouse_last_name,
                          full_name: `${formik.values.spouse_first_name} ${formik.values.spouse_middle_name} ${formik.values.spouse_last_name}`.trim(),
                          digital_id: formik.values.spouse_digital_id || `SPOUSE_${formik.values.spouse_id}` // Use actual digital_id if available
                        } : null}
                        onChange={(event, newValue) => {
                          if (newValue) {
                            formik.setValues({
                              ...formik.values,
                              spouse_id: newValue.id, // Use database id for linked_citizen
                              spouse_digital_id: newValue.digital_id, // Store digital_id for display
                              spouse_first_name: newValue.first_name,
                              spouse_middle_name: newValue.middle_name,
                              spouse_last_name: newValue.last_name,
                              // Copy contact info from selected resident
                              spouse_phone: newValue.phone || '',
                              spouse_email: newValue.email || '',
                            });
                          } else {
                            formik.setValues({
                              ...formik.values,
                              spouse_id: '',
                              spouse_digital_id: '',
                              spouse_first_name: '',
                              spouse_middle_name: '',
                              spouse_last_name: '',
                              spouse_phone: '',
                              spouse_email: '',
                            });
                          }
                        }}
                        disabled={!formik.values.has_spouse}
                        required={formik.values.has_spouse && formik.values.is_spouse_resident}
                        sx={{ mb: 2 }}
                      />
                    </Box>
                  ) : (
                    <Box>
                      <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                        Spouse Personal Information
                      </Typography>
                      <Grid container spacing={2} sx={{ mb: 3 }}>
                        <Grid item xs={12} md={4}>
                          <TextField
                            fullWidth
                            id="spouse_first_name"
                            name="spouse_first_name"
                            label="First Name"
                            value={formik.values.spouse_first_name}
                            onChange={formik.handleChange}
                            error={formik.touched.spouse_first_name && Boolean(formik.errors.spouse_first_name)}
                            helperText={formik.touched.spouse_first_name && formik.errors.spouse_first_name}
                            required={formik.values.has_spouse && !formik.values.is_spouse_resident}
                          />
                        </Grid>
                        <Grid item xs={12} md={4}>
                          <TextField
                            fullWidth
                            id="spouse_middle_name"
                            name="spouse_middle_name"
                            label="Middle Name"
                            value={formik.values.spouse_middle_name}
                            onChange={formik.handleChange}
                            error={formik.touched.spouse_middle_name && Boolean(formik.errors.spouse_middle_name)}
                            helperText={formik.touched.spouse_middle_name && formik.errors.spouse_middle_name}
                            required={formik.values.has_spouse && !formik.values.is_spouse_resident}
                          />
                        </Grid>
                        <Grid item xs={12} md={4}>
                          <TextField
                            fullWidth
                            id="spouse_last_name"
                            name="spouse_last_name"
                            label="Last Name"
                            value={formik.values.spouse_last_name}
                            onChange={formik.handleChange}
                            error={formik.touched.spouse_last_name && Boolean(formik.errors.spouse_last_name)}
                            helperText={formik.touched.spouse_last_name && formik.errors.spouse_last_name}
                            required={formik.values.has_spouse && !formik.values.is_spouse_resident}
                          />
                        </Grid>
                      </Grid>

                      <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                        Spouse Contact Information
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            id="spouse_phone"
                            name="spouse_phone"
                            label="Phone Number"
                            value={formik.values.spouse_phone}
                            onChange={formik.handleChange}
                            error={formik.touched.spouse_phone && Boolean(formik.errors.spouse_phone)}
                            helperText={formik.touched.spouse_phone && formik.errors.spouse_phone}
                            placeholder="Enter phone number"
                          />
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            id="spouse_email"
                            name="spouse_email"
                            label="Email Address"
                            type="email"
                            value={formik.values.spouse_email}
                            onChange={formik.handleChange}
                            error={formik.touched.spouse_email && Boolean(formik.errors.spouse_email)}
                            helperText={formik.touched.spouse_email && formik.errors.spouse_email}
                            placeholder="Enter email address"
                          />
                        </Grid>
                      </Grid>
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                        * At least one contact method (phone or email) is required
                      </Typography>
                    </Box>
                  )}
                </Box>
            )}
          </CardContent>
        </Card>
        )}

        {/* Parents Information - Now Mandatory */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <PeopleIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6" component="h2">
                Parents Information
              </Typography>
              <Typography variant="body2" color="error" sx={{ ml: 1 }}>
                (Required)
              </Typography>
            </Box>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Parent information is required for fraud prevention and accurate citizen identification.
            </Typography>
            {/* Father Information */}
            <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
              Father Information
            </Typography>
                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formik.values.is_father_resident}
                            onChange={(e) => {
                              formik.setFieldValue('is_father_resident', e.target.checked);
                              if (!e.target.checked) {
                                formik.setFieldValue('father_id', '');
                              }
                            }}
                            color="primary"
                          />
                        }
                        label="Father is a resident of this kebele"
                      />
                    </Grid>
                  </Grid>

                  {formik.values.is_father_resident ? (
                    <Box sx={{ mb: 2 }}>
                      <ResidentAutocomplete
                        label="Search for Father"
                        placeholder="Type to search for father..."
                        gender="male"
                        value={formik.values.father_id ? {
                          id: formik.values.father_id, // This is the database ID
                          first_name: formik.values.father_first_name,
                          middle_name: formik.values.father_middle_name,
                          last_name: formik.values.father_last_name,
                          full_name: `${formik.values.father_first_name} ${formik.values.father_middle_name} ${formik.values.father_last_name}`.trim(),
                          digital_id: formik.values.father_digital_id || `FATHER_${formik.values.father_id}`, // Use actual digital_id if available
                          gender: 'male'
                        } : null}
                        onChange={(event, newValue) => {
                          if (newValue) {
                            formik.setValues({
                              ...formik.values,
                              father_id: newValue.id, // Use database id for linked_citizen
                              father_digital_id: newValue.digital_id, // Store digital_id for display
                              father_first_name: newValue.first_name,
                              father_middle_name: newValue.middle_name,
                              father_last_name: newValue.last_name,
                              // Copy contact info from selected resident
                              father_phone: newValue.phone || '',
                              father_email: newValue.email || '',
                            });
                          } else {
                            formik.setValues({
                              ...formik.values,
                              father_id: '',
                              father_digital_id: '',
                              father_first_name: '',
                              father_middle_name: '',
                              father_last_name: '',
                              father_phone: '',
                              father_email: '',
                            });
                          }
                        }}
                        required={formik.values.is_father_resident}
                        sx={{ mb: 2 }}
                      />
                    </Box>
                  ) : (
                    <Grid container spacing={2} sx={{ mb: 3 }}>
                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          id="father_first_name"
                          name="father_first_name"
                          label="Father's First Name"
                          value={formik.values.father_first_name}
                          onChange={formik.handleChange}
                          error={formik.touched.father_first_name && Boolean(formik.errors.father_first_name)}
                          helperText={formik.touched.father_first_name && formik.errors.father_first_name}
                          required={!formik.values.is_father_resident}
                        />
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          id="father_middle_name"
                          name="father_middle_name"
                          label="Father's Middle Name"
                          value={formik.values.father_middle_name}
                          onChange={formik.handleChange}
                          error={formik.touched.father_middle_name && Boolean(formik.errors.father_middle_name)}
                          helperText={formik.touched.father_middle_name && formik.errors.father_middle_name}
                        />
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          id="father_last_name"
                          name="father_last_name"
                          label="Father's Last Name"
                          value={formik.values.father_last_name}
                          onChange={formik.handleChange}
                          error={formik.touched.father_last_name && Boolean(formik.errors.father_last_name)}
                          helperText={formik.touched.father_last_name && formik.errors.father_last_name}
                          required={!formik.values.is_father_resident}
                        />
                      </Grid>
                    </Grid>
                  )}

                  <Divider sx={{ my: 3 }} />

                  {/* Mother Information */}
                  <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                    Mother Information
                  </Typography>
                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formik.values.is_mother_resident}
                            onChange={(e) => {
                              formik.setFieldValue('is_mother_resident', e.target.checked);
                              if (!e.target.checked) {
                                formik.setFieldValue('mother_id', '');
                              }
                            }}
                            color="primary"
                          />
                        }
                        label="Mother is a resident of this kebele"
                      />
                    </Grid>
                  </Grid>

                  {formik.values.is_mother_resident ? (
                    <Box sx={{ mb: 2 }}>
                      <ResidentAutocomplete
                        label="Search for Mother"
                        placeholder="Type to search for mother..."
                        gender="female"
                        value={formik.values.mother_id ? {
                          id: formik.values.mother_id, // This is the database ID
                          first_name: formik.values.mother_first_name,
                          middle_name: formik.values.mother_middle_name,
                          last_name: formik.values.mother_last_name,
                          full_name: `${formik.values.mother_first_name} ${formik.values.mother_middle_name} ${formik.values.mother_last_name}`.trim(),
                          digital_id: formik.values.mother_digital_id || `MOTHER_${formik.values.mother_id}`, // Use actual digital_id if available
                          gender: 'female'
                        } : null}
                        onChange={(event, newValue) => {
                          if (newValue) {
                            formik.setValues({
                              ...formik.values,
                              mother_id: newValue.id, // Use database id for linked_citizen
                              mother_digital_id: newValue.digital_id, // Store digital_id for display
                              mother_first_name: newValue.first_name,
                              mother_middle_name: newValue.middle_name,
                              mother_last_name: newValue.last_name,
                              // Copy contact info from selected resident
                              mother_phone: newValue.phone || '',
                              mother_email: newValue.email || '',
                            });
                          } else {
                            formik.setValues({
                              ...formik.values,
                              mother_id: '',
                              mother_digital_id: '',
                              mother_first_name: '',
                              mother_middle_name: '',
                              mother_last_name: '',
                              mother_phone: '',
                              mother_email: '',
                            });
                          }
                        }}
                        required={formik.values.is_mother_resident}
                        sx={{ mb: 2 }}
                      />
                    </Box>
                  ) : (
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          id="mother_first_name"
                          name="mother_first_name"
                          label="Mother's First Name"
                          value={formik.values.mother_first_name}
                          onChange={formik.handleChange}
                          error={formik.touched.mother_first_name && Boolean(formik.errors.mother_first_name)}
                          helperText={formik.touched.mother_first_name && formik.errors.mother_first_name}
                          required={!formik.values.is_mother_resident}
                        />
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          id="mother_middle_name"
                          name="mother_middle_name"
                          label="Mother's Middle Name"
                          value={formik.values.mother_middle_name}
                          onChange={formik.handleChange}
                          error={formik.touched.mother_middle_name && Boolean(formik.errors.mother_middle_name)}
                          helperText={formik.touched.mother_middle_name && formik.errors.mother_middle_name}
                        />
                      </Grid>
                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          id="mother_last_name"
                          name="mother_last_name"
                          label="Mother's Last Name"
                          value={formik.values.mother_last_name}
                          onChange={formik.handleChange}
                          error={formik.touched.mother_last_name && Boolean(formik.errors.mother_last_name)}
                          helperText={formik.touched.mother_last_name && formik.errors.mother_last_name}
                          required={!formik.values.is_mother_resident}
                        />
                      </Grid>
                    </Grid>
                  )}
          </CardContent>
        </Card>

        {/* Children Information */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <ChildIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6" component="h2">
                Children Information
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                (Optional)
              </Typography>
            </Box>

            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formik.values.has_children}
                      onChange={(e) => {
                        formik.setFieldValue('has_children', e.target.checked);
                        if (!e.target.checked) {
                          formik.setFieldValue('children', []);
                        }
                      }}
                      color="primary"
                    />
                  }
                  label="Citizen has children"
                />
              </Grid>
            </Grid>

            {formik.values.has_children && (
                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="subtitle1" fontWeight={600}>
                      Children ({formik.values.children.length})
                    </Typography>
                    <Button
                      startIcon={<AddIcon />}
                      onClick={addChild}
                      variant="outlined"
                      size="small"
                    >
                      Add Child
                    </Button>
                  </Box>

                  {formik.values.children.map((child, index) => (
                    <Paper key={index} variant="outlined" sx={{ p: 2, mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="subtitle2">
                          Child {index + 1}
                        </Typography>
                        <IconButton
                          onClick={() => removeChild(index)}
                          color="error"
                          size="small"
                        >
                          <RemoveIcon />
                        </IconButton>
                      </Box>

                      <Grid container spacing={2} sx={{ mb: 2 }}>
                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={child.is_resident}
                                onChange={(e) => {
                                  const updatedChildren = [...formik.values.children];
                                  updatedChildren[index].is_resident = e.target.checked;
                                  if (!e.target.checked) {
                                    updatedChildren[index].child_id = '';
                                  }
                                  formik.setFieldValue('children', updatedChildren);
                                }}
                                color="primary"
                              />
                            }
                            label="Child is a resident of this kebele"
                          />
                        </Grid>
                      </Grid>

                      {child.is_resident ? (
                        <Box sx={{ mb: 2 }}>
                          <Typography variant="body2" color="text.secondary">
                            Child resident search functionality will be implemented in a future update.
                          </Typography>
                        </Box>
                      ) : (
                        <Grid container spacing={2}>
                          <Grid item xs={12} md={3}>
                            <TextField
                              fullWidth
                              label="First Name"
                              value={child.first_name}
                              onChange={(e) => {
                                const updatedChildren = [...formik.values.children];
                                updatedChildren[index].first_name = e.target.value;
                                formik.setFieldValue('children', updatedChildren);
                              }}
                            />
                          </Grid>
                          <Grid item xs={12} md={3}>
                            <TextField
                              fullWidth
                              label="Middle Name"
                              value={child.middle_name}
                              onChange={(e) => {
                                const updatedChildren = [...formik.values.children];
                                updatedChildren[index].middle_name = e.target.value;
                                formik.setFieldValue('children', updatedChildren);
                              }}
                            />
                          </Grid>
                          <Grid item xs={12} md={3}>
                            <TextField
                              fullWidth
                              label="Last Name"
                              value={child.last_name}
                              onChange={(e) => {
                                const updatedChildren = [...formik.values.children];
                                updatedChildren[index].last_name = e.target.value;
                                formik.setFieldValue('children', updatedChildren);
                              }}
                            />
                          </Grid>
                          <Grid item xs={12} md={3}>
                            <TextField
                              fullWidth
                              select
                              label="Gender"
                              value={child.gender}
                              onChange={(e) => {
                                const updatedChildren = [...formik.values.children];
                                updatedChildren[index].gender = e.target.value;
                                formik.setFieldValue('children', updatedChildren);
                              }}
                            >
                              <MenuItem value="male">Male</MenuItem>
                              <MenuItem value="female">Female</MenuItem>
                            </TextField>
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <DatePicker
                              label="Date of Birth"
                              value={child.date_of_birth}
                              onChange={(value) => {
                                const updatedChildren = [...formik.values.children];
                                updatedChildren[index].date_of_birth = value;
                                formik.setFieldValue('children', updatedChildren);
                              }}
                              slotProps={{
                                textField: {
                                  fullWidth: true
                                }
                              }}
                            />
                          </Grid>
                        </Grid>
                      )}
                    </Paper>
                  ))}
                </Box>
            )}
          </CardContent>
        </Card>
      </Box>
    </LocalizationProvider>
  );
};

export default FamilyInfoStep;
