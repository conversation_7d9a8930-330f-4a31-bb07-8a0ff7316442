from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db import transaction
from django.db.models import Q, Count, Case, When, IntegerField
from django.db.models.functions import TruncMonth
from django.shortcuts import get_object_or_404
from django_tenants.utils import schema_context
from datetime import datetime, timedelta
from django.utils import timezone
from ..models.tenant import Tenant
# Note: We import tenant-specific models dynamically within schema_context
# to ensure they use the correct tenant schema
from ..serializers.citizen_serializer import (
    TenantCitizenSerializer, CitizenCreateSerializer,
    EmergencyContactSerializer, ParentSerializer, ChildSerializer,
    SpouseSerializer, BiometricSerializer, PhotoSerializer, DocumentSerializer
)
from ..permissions import CanManageTenants
from common.permissions import CanManageCitizens


class TenantSpecificCitizenViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing citizens within a specific tenant schema.
    URL pattern: /api/tenants/{tenant_id}/citizens/
    """
    permission_classes = [permissions.IsAuthenticated, CanManageCitizens]

    def get_serializer_class(self):
        """Return different serializers for different actions."""
        if self.action == 'create':
            return CitizenCreateSerializer
        return TenantCitizenSerializer

    def get_tenant(self):
        """Get the tenant from URL parameter."""
        tenant_id = self.kwargs.get('tenant_id')
        return get_object_or_404(Tenant, id=tenant_id)

    def get_queryset(self):
        """Get citizens from the specific tenant schema."""
        tenant = self.get_tenant()

        # Switch to tenant schema and get citizens
        with schema_context(tenant.schema_name):
            from citizens.models import Citizen
            from idcards.models import IDCard, IDCardStatus

            # By default, only show active citizens (hide transferred citizens)
            show_transferred = self.request.query_params.get('show_transferred', 'false').lower() == 'true'
            if show_transferred:
                queryset = Citizen.objects.all()  # Show all including transferred
            else:
                queryset = Citizen.objects.filter(is_active=True)  # Only active citizens

            # Apply role-based filtering
            user = self.request.user
            if user.role == 'kebele_leader':
                # Kebele leaders can see citizens whose ID cards are pending approval OR already approved (for reporting)
                relevant_idcard_citizen_ids = IDCard.objects.filter(
                    status__in=[IDCardStatus.PENDING_APPROVAL, IDCardStatus.KEBELE_APPROVED, IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED]
                ).values_list('citizen_id', flat=True)
                queryset = queryset.filter(id__in=relevant_idcard_citizen_ids)
            elif user.role in ['clerk', 'kebele_admin', 'subcity_admin', 'city_admin', 'superadmin'] or user.is_superuser:
                # Clerks, admins and superusers can see all citizens
                pass  # No filtering, show all

            return queryset

    def list(self, request, *args, **kwargs):
        """List citizens in the tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)

    def retrieve(self, request, *args, **kwargs):
        """Retrieve a specific citizen from the tenant schema with expanded foreign keys."""
        tenant = self.get_tenant()
        citizen_id = kwargs.get('pk')

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen
            citizen = get_object_or_404(Citizen, id=citizen_id)

            # Get the citizen data
            serializer = self.get_serializer(citizen)
            citizen_data = serializer.data

            # Expand foreign key relationships if requested
            expand = request.query_params.get('expand', '')
            print(f"🔍 Expand parameter received: '{expand}'")
            print(f"🔍 Original citizen data subcity: {citizen_data.get('subcity')}")
            print(f"🔍 Original citizen data kebele: {citizen_data.get('kebele')}")

            if expand:
                print(f"🔍 Calling _expand_foreign_keys with fields: {expand.split(',')}")
                citizen_data = self._expand_foreign_keys(citizen_data, expand.split(','))
                print(f"🔍 After expansion subcity: {citizen_data.get('subcity')}")
                print(f"🔍 After expansion kebele: {citizen_data.get('kebele')}")
            else:
                print(f"❌ No expand parameter provided - skipping foreign key expansion")

            return Response(citizen_data)

    def _expand_foreign_keys(self, citizen_data, expand_fields):
        """Expand foreign key fields with actual object data."""
        from shared.models import Religion, MaritalStatus, CitizenStatus, Country, Region
        from tenants.models import SubCity, Kebele

        print(f"🔍 _expand_foreign_keys called with fields: {expand_fields}")
        print(f"🔍 citizen_data keys: {list(citizen_data.keys())}")

        # Debug: List available subcities and kebeles
        try:
            all_subcities = SubCity.objects.all()[:10]
            all_kebeles = Kebele.objects.all()[:10]
            print(f"🔍 Available subcities: {[(s.id, s.name) for s in all_subcities]}")
            print(f"🔍 Available kebeles: {[(k.id, k.name) for k in all_kebeles]}")
        except Exception as e:
            print(f"❌ Error listing available data: {e}")

        for field in expand_fields:
            field = field.strip()
            if field == 'religion' and citizen_data.get('religion'):
                try:
                    religion = Religion.objects.get(id=citizen_data['religion'])
                    citizen_data['religion'] = {'id': religion.id, 'name': religion.name}
                except Religion.DoesNotExist:
                    pass
            elif field == 'marital_status' and citizen_data.get('marital_status'):
                try:
                    marital_status = MaritalStatus.objects.get(id=citizen_data['marital_status'])
                    citizen_data['marital_status'] = {'id': marital_status.id, 'name': marital_status.name}
                except MaritalStatus.DoesNotExist:
                    pass
            elif field == 'citizen_status' and citizen_data.get('status'):
                try:
                    citizen_status = CitizenStatus.objects.get(id=citizen_data['status'])
                    citizen_data['citizen_status'] = {'id': citizen_status.id, 'name': citizen_status.name}
                except CitizenStatus.DoesNotExist:
                    pass
            elif field == 'nationality' and citizen_data.get('nationality'):
                try:
                    country = Country.objects.get(id=citizen_data['nationality'])
                    citizen_data['nationality'] = {'id': country.id, 'name': country.name}
                except Country.DoesNotExist:
                    pass
            elif field == 'city' and citizen_data.get('city'):
                try:
                    # City data is in public schema
                    from tenants.models import CityAdministration
                    city = CityAdministration.objects.get(id=citizen_data['city'])
                    citizen_data['city'] = {'id': city.id, 'name': city.name}
                    print(f"🔍 Expanded city: {citizen_data['city']}")
                except CityAdministration.DoesNotExist:
                    print(f"❌ City with ID {citizen_data['city']} not found")
                    pass
            elif field == 'subcity' and citizen_data.get('subcity'):
                try:
                    # The subcity field contains tenant_id, not subcity model id
                    # We need to find the SubCity record by its tenant relationship
                    from tenants.models import SubCity, Tenant

                    # First try to find by tenant_id (the correct way)
                    try:
                        tenant = Tenant.objects.get(id=citizen_data['subcity'])
                        subcity = SubCity.objects.get(tenant=tenant)
                        citizen_data['subcity'] = {'id': subcity.id, 'name': subcity.name, 'tenant_id': tenant.id}
                        print(f"🔍 Expanded subcity by tenant: {citizen_data['subcity']}")
                    except (Tenant.DoesNotExist, SubCity.DoesNotExist):
                        # Fallback: try direct ID lookup (legacy)
                        subcity = SubCity.objects.get(id=citizen_data['subcity'])
                        citizen_data['subcity'] = {'id': subcity.id, 'name': subcity.name}
                        print(f"🔍 Expanded subcity by direct ID: {citizen_data['subcity']}")

                except SubCity.DoesNotExist:
                    print(f"❌ SubCity with tenant ID {citizen_data['subcity']} not found")
                    # Debug: List available subcities and their tenant relationships
                    try:
                        from tenants.models import SubCity
                        all_subcities = SubCity.objects.select_related('tenant').all()[:5]
                        print(f"🔍 Available subcities: {[(s.id, s.name, s.tenant.id if s.tenant else None) for s in all_subcities]}")
                    except Exception as e:
                        print(f"❌ Error listing subcities: {e}")
                    pass
            elif field == 'kebele' and citizen_data.get('kebele'):
                try:
                    # The kebele field contains tenant_id, not kebele model id
                    # We need to find the Kebele record by its tenant relationship
                    from tenants.models import Kebele, Tenant

                    # First try to find by tenant_id (the correct way)
                    try:
                        tenant = Tenant.objects.get(id=citizen_data['kebele'])
                        kebele = Kebele.objects.get(tenant=tenant)
                        citizen_data['kebele'] = {'id': kebele.id, 'name': kebele.name, 'tenant_id': tenant.id}
                        print(f"🔍 Expanded kebele by tenant: {citizen_data['kebele']}")
                    except (Tenant.DoesNotExist, Kebele.DoesNotExist):
                        # Fallback: try direct ID lookup (legacy)
                        kebele = Kebele.objects.get(id=citizen_data['kebele'])
                        citizen_data['kebele'] = {'id': kebele.id, 'name': kebele.name}
                        print(f"🔍 Expanded kebele by direct ID: {citizen_data['kebele']}")

                except Kebele.DoesNotExist:
                    print(f"❌ Kebele with tenant ID {citizen_data['kebele']} not found")
                    # Debug: List available kebeles and their tenant relationships
                    try:
                        from tenants.models import Kebele
                        all_kebeles = Kebele.objects.select_related('tenant').all()[:5]
                        print(f"🔍 Available kebeles: {[(k.id, k.name, k.tenant.id if k.tenant else None) for k in all_kebeles]}")
                    except Exception as e:
                        print(f"❌ Error listing kebeles: {e}")
                    pass

        return citizen_data

    @transaction.atomic
    def create(self, request, *args, **kwargs):
        """Create a new citizen with all related family information in the tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            # Import the tenant-specific models from citizens app (TENANT_APPS)
            from citizens.models import Citizen, EmergencyContact, Parent, Child, Spouse
            from idcards.fraud_detection import fraud_detection_service

            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # Extract nested data before creating citizen
            validated_data = serializer.validated_data.copy()
            children_data = validated_data.pop('children_data', [])
            parents_data = validated_data.pop('parents_data', [])
            emergency_contacts_data = validated_data.pop('emergency_contacts_data', [])
            spouse_data = validated_data.pop('spouse_data', None)

            # FRAUD DETECTION: Check for potential fraud before creating citizen
            # Extract parent information for more reliable fraud detection
            mother_full_name = ""
            father_full_name = ""

            if parents_data:
                for parent in parents_data:
                    parent_name = f"{parent.get('first_name', '')} {parent.get('middle_name', '')} {parent.get('last_name', '')}".strip()
                    if parent.get('relationship') == 'mother':
                        mother_full_name = parent_name
                    elif parent.get('relationship') == 'father':
                        father_full_name = parent_name

            citizen_data = {
                'digital_id': validated_data.get('digital_id'),  # May be None, will be generated
                'first_name': validated_data.get('first_name'),
                'middle_name': validated_data.get('middle_name'),
                'last_name': validated_data.get('last_name'),
                'date_of_birth': validated_data.get('date_of_birth'),
                'mother_full_name': mother_full_name,
                'father_full_name': father_full_name,
                'photo': validated_data.get('photo')  # Photo data for comparison
            }

            # Run fraud detection
            fraud_results = fraud_detection_service.check_fraud_indicators(citizen_data, tenant.id)

            # Handle fraud detection results
            if fraud_results['risk_level'] == 'critical':
                # Block registration for critical fraud
                return Response({
                    'error': 'Registration blocked due to fraud detection',
                    'fraud_detected': True,
                    'fraud_details': fraud_results,
                    'message': 'This citizen appears to already be registered in another kebele. Please verify identity and contact the other kebele for clarification.'
                }, status=status.HTTP_403_FORBIDDEN)

            elif fraud_results['risk_level'] in ['high', 'medium']:
                # Log the fraud warning but allow registration with warning
                print(f"⚠️ FRAUD WARNING: {fraud_results['risk_level'].upper()} risk detected for citizen registration")
                print(f"   Indicators: {fraud_results['indicators']}")
                print(f"   Matches: {len(fraud_results['matches'])}")

            # Generate digital_id if not provided
            if not validated_data.get('digital_id'):
                # The digital_id will be generated automatically in the Citizen.save() method
                # based on the tenant hierarchy (city + subcity + kebele + random digits)
                pass

            # Create the citizen directly in the tenant schema
            citizen = Citizen.objects.create(**validated_data)

            # Create related objects in the same schema context
            self._create_related_objects_in_schema(citizen, children_data, parents_data, emergency_contacts_data, spouse_data)

            # Prepare response with fraud warning if applicable
            response_serializer = TenantCitizenSerializer(citizen)
            response_data = response_serializer.data

            # Include fraud warning in response if risk detected
            if fraud_results['risk_level'] != 'low':
                response_data['fraud_warning'] = {
                    'risk_level': fraud_results['risk_level'],
                    'indicators': fraud_results['indicators'],
                    'recommendations': fraud_results['recommendations'],
                    'matches_found': len(fraud_results['matches'])
                }

            return Response(response_data, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=['post'])
    def check_registration_fraud(self, request, tenant_id=None):
        """
        Check for fraud indicators during citizen registration.
        This endpoint allows clerks to check for potential fraud in real-time
        as they fill out the registration form.
        """
        tenant = self.get_tenant()

        try:
            from idcards.fraud_detection import fraud_detection_service

            # Extract citizen data from request - focus on reliable identifiers
            citizen_data = {
                'digital_id': request.data.get('digital_id'),
                'first_name': request.data.get('first_name'),
                'middle_name': request.data.get('middle_name'),
                'last_name': request.data.get('last_name'),
                'date_of_birth': request.data.get('date_of_birth'),
                'mother_full_name': request.data.get('mother_full_name'),
                'father_full_name': request.data.get('father_full_name'),
                'photo': request.data.get('photo')
            }

            # Validate that we have enough data for meaningful fraud detection
            required_fields = ['first_name', 'last_name']
            missing_fields = [field for field in required_fields if not citizen_data.get(field)]

            if missing_fields:
                return Response({
                    'error': f'Missing required fields for fraud detection: {", ".join(missing_fields)}',
                    'fraud_check_possible': False
                }, status=status.HTTP_400_BAD_REQUEST)

            # Run fraud detection
            fraud_results = fraud_detection_service.check_fraud_indicators(citizen_data, tenant.id)

            # Return results with user-friendly messages
            response_data = {
                'fraud_check_completed': True,
                'risk_level': fraud_results['risk_level'],
                'fraud_detected': fraud_results['is_fraud_detected'],
                'can_proceed': fraud_results['risk_level'] != 'critical',
                'warnings': [],
                'recommendations': fraud_results['recommendations']
            }

            # Add user-friendly warnings based on risk level
            if fraud_results['risk_level'] == 'critical':
                response_data['warnings'].append({
                    'type': 'critical',
                    'title': 'Registration Blocked',
                    'message': 'This citizen appears to already be registered in another kebele. Registration cannot proceed.',
                    'action_required': 'Contact the other kebele to verify citizen status before proceeding.'
                })
            elif fraud_results['risk_level'] == 'high':
                response_data['warnings'].append({
                    'type': 'high',
                    'title': 'High Risk Detected',
                    'message': 'Similar citizen information found in another kebele. Please verify identity carefully.',
                    'action_required': 'Request additional identification documents and verify with the citizen.'
                })
            elif fraud_results['risk_level'] == 'medium':
                response_data['warnings'].append({
                    'type': 'medium',
                    'title': 'Potential Duplicate Detected',
                    'message': 'Some citizen information matches records in other kebeles.',
                    'action_required': 'Double-check citizen information and ask about previous registrations.'
                })

            # Include match details for staff review
            if fraud_results['matches']:
                response_data['matches'] = []
                for match in fraud_results['matches']:
                    response_data['matches'].append({
                        'kebele_name': match['tenant_name'],
                        'citizen_name': match['citizen_name'],
                        'match_type': match['type'],
                        'confidence': f"{match['match_confidence']*100:.0f}%",
                        'details': match['details']
                    })

            return Response(response_data)

        except Exception as e:
            return Response({
                'error': f'Fraud detection failed: {str(e)}',
                'fraud_check_completed': False,
                'can_proceed': True  # Allow registration if fraud detection fails
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _create_related_objects_in_schema(self, citizen, children_data, parents_data, emergency_contacts_data, spouse_data):
        """Create related objects within the tenant schema context."""
        from citizens.models import EmergencyContact, Parent, Child, Spouse

        # Create emergency contacts
        for contact_data in emergency_contacts_data:
            EmergencyContact.objects.create(citizen=citizen, **contact_data)

        # Create parents
        for parent_data in parents_data:
            Parent.objects.create(citizen=citizen, **parent_data)

        # Create children
        for child_data in children_data:
            Child.objects.create(citizen=citizen, **child_data)

        # Create spouse
        if spouse_data:
            print(f"🔍 Creating spouse for citizen {citizen.id} with data: {spouse_data}")
            try:
                # Validate the data before creating
                from django.core.exceptions import ValidationError

                # Check required fields
                if not spouse_data.get('first_name') or not spouse_data.get('last_name'):
                    print(f"❌ Spouse validation failed: Missing required fields (first_name or last_name)")
                    print(f"   - first_name: '{spouse_data.get('first_name')}'")
                    print(f"   - last_name: '{spouse_data.get('last_name')}'")
                    return

                # Check contact method requirement
                if not spouse_data.get('phone') and not spouse_data.get('email'):
                    print(f"❌ Spouse validation failed: No contact method provided")
                    print(f"   - phone: '{spouse_data.get('phone')}'")
                    print(f"   - email: '{spouse_data.get('email')}'")
                    return

                # Create the spouse
                spouse = Spouse.objects.create(citizen=citizen, **spouse_data)
                print(f"✅ Spouse created successfully with ID: {spouse.id}")

            except ValidationError as ve:
                print(f"❌ Spouse validation error: {ve}")
            except Exception as e:
                print(f"❌ Spouse creation failed with exception: {e}")
                print(f"   Exception type: {type(e).__name__}")
                import traceback
                print(f"   Traceback: {traceback.format_exc()}")
        else:
            print(f"❌ No spouse data provided for citizen {citizen.id}")

    def _create_family_members(self, citizen, data):
        """Create family members (emergency contacts, parents, spouse, children)."""

        # Create emergency contacts
        emergency_contacts_data = data.get('emergency_contacts_data', [])
        for contact_data in emergency_contacts_data:
            contact_data['citizen'] = citizen.id
            contact_serializer = EmergencyContactSerializer(data=contact_data)
            if contact_serializer.is_valid():
                contact_serializer.save()

        # Create parents
        parents_data = data.get('parents_data', [])
        for parent_data in parents_data:
            parent_data['citizen'] = citizen.id
            parent_serializer = ParentSerializer(data=parent_data)
            if parent_serializer.is_valid():
                parent_serializer.save()

        # Create spouse
        spouse_data = data.get('spouse_data')
        if spouse_data:
            print(f"🔍 Creating spouse with data: {spouse_data}")
            spouse_data['citizen'] = citizen.id
            from ..serializers.citizen_serializer import SpouseCreateSerializer
            spouse_serializer = SpouseCreateSerializer(data=spouse_data)
            if spouse_serializer.is_valid():
                spouse_serializer.save()
                print(f"✅ Spouse created successfully")
            else:
                print(f"❌ Spouse validation failed: {spouse_serializer.errors}")

        # Create children
        children_data = data.get('children_data', [])
        for child_data in children_data:
            child_data['citizen'] = citizen.id
            child_serializer = ChildSerializer(data=child_data)
            if child_serializer.is_valid():
                child_serializer.save()

    def update(self, request, *args, **kwargs):
        """Update a citizen in the tenant schema."""
        tenant = self.get_tenant()
        citizen_id = kwargs.get('pk')

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen
            citizen = get_object_or_404(Citizen, id=citizen_id)
            serializer = self.get_serializer(citizen, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        """Delete a citizen from the tenant schema."""
        tenant = self.get_tenant()
        citizen_id = kwargs.get('pk')

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen
            citizen = get_object_or_404(Citizen, id=citizen_id)
            citizen.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=False, methods=['get'])
    def search(self, request, *args, **kwargs):
        """Search for citizens in the tenant schema."""
        tenant = self.get_tenant()
        search_term = request.query_params.get('search', '')
        gender = request.query_params.get('gender', None)

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen
            queryset = Citizen.objects.all()

            if search_term:
                queryset = queryset.filter(
                    first_name__icontains=search_term
                ) | queryset.filter(
                    middle_name__icontains=search_term
                ) | queryset.filter(
                    last_name__icontains=search_term
                ) | queryset.filter(
                    digital_id__icontains=search_term
                )

            if gender:
                # Filter by gender
                queryset = queryset.filter(gender=gender)

            serializer = self.get_serializer(queryset[:20], many=True)  # Limit to 20 results
            return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def stats(self, request, *args, **kwargs):
        """Get statistics for citizens in the tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen
            from django.db.models import Count, Q

            total = Citizen.objects.count()
            male = Citizen.objects.filter(gender='male').count()
            female = Citizen.objects.filter(gender='female').count()

            # Note: Assuming there's an id_cards relationship or field
            # This might need adjustment based on your actual model structure
            with_id_cards = 0  # Placeholder - adjust based on your ID card model

            stats = {
                'total': total,
                'male': male,
                'female': female,
                'withIdCards': with_id_cards
            }

            return Response(stats)

    @action(detail=False, methods=['get'], url_path='dashboard/reports')
    def dashboard_reports(self, request, *args, **kwargs):
        """Get comprehensive dashboard reports for kebele-level monitoring."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen
            from idcards.models import IDCard, IDCardStatus
            from shared.models import Ketena

            # Current date for calculations
            now = timezone.now()
            thirty_days_ago = now - timedelta(days=30)
            thirty_days_from_now = now + timedelta(days=30)

            # 1. Total Registered Citizens in Kebele
            total_citizens = Citizen.objects.count()

            # 2. Population by Ketena
            population_by_ketena = []
            try:
                # Since ketena is an integer reference, we need to get the actual ketena names from shared schema
                ketena_stats = Citizen.objects.values('ketena').annotate(
                    population=Count('id')
                ).order_by('-population')

                print(f"🔍 Ketena stats raw data: {list(ketena_stats)}")

                for stat in ketena_stats:
                    if stat['ketena']:
                        try:
                            # Get ketena name from shared schema
                            from shared.models import Ketena
                            ketena_obj = Ketena.objects.get(id=stat['ketena'])
                            population_count = int(stat['population'])  # Ensure it's an integer
                            population_by_ketena.append({
                                'name': ketena_obj.name,
                                'population': population_count
                            })
                            print(f"✅ Added ketena: {ketena_obj.name} with population: {population_count}")
                        except Ketena.DoesNotExist:
                            # Fallback to ketena ID if name not found
                            population_count = int(stat['population'])  # Ensure it's an integer
                            population_by_ketena.append({
                                'name': f'Ketena {stat["ketena"]}',
                                'population': population_count
                            })
                            print(f"✅ Added fallback ketena: Ketena {stat['ketena']} with population: {population_count}")

                print(f"🔍 Final population_by_ketena: {population_by_ketena}")
            except Exception as e:
                print(f"❌ Error calculating ketena population: {e}")
                import traceback
                print(f"❌ Traceback: {traceback.format_exc()}")
                # Fallback with mock data
                population_by_ketena = [
                    {'name': 'Ketena 01', 'population': 150},
                    {'name': 'Ketena 02', 'population': 120},
                    {'name': 'Ketena 03', 'population': 98},
                ]

            # 3. Top 3 Populated Ketenas
            top_ketenas = population_by_ketena[:3]

            # 4. Gender Ratio (already calculated in stats)
            male_count = Citizen.objects.filter(gender='male').count()
            female_count = Citizen.objects.filter(gender='female').count()

            # 5. Age Group Distribution
            age_groups = []
            try:
                # Calculate age groups based on date_of_birth
                age_group_stats = Citizen.objects.extra(
                    select={
                        'age': "EXTRACT(year FROM AGE(date_of_birth))"
                    }
                ).extra(
                    select={
                        'age_group': """
                        CASE
                            WHEN EXTRACT(year FROM AGE(date_of_birth)) < 18 THEN 'Under 18'
                            WHEN EXTRACT(year FROM AGE(date_of_birth)) BETWEEN 18 AND 30 THEN '18-30'
                            WHEN EXTRACT(year FROM AGE(date_of_birth)) BETWEEN 31 AND 50 THEN '31-50'
                            WHEN EXTRACT(year FROM AGE(date_of_birth)) BETWEEN 51 AND 65 THEN '51-65'
                            ELSE 'Over 65'
                        END
                        """
                    }
                ).values('age_group').annotate(count=Count('id'))

                for stat in age_group_stats:
                    age_groups.append({
                        'name': stat['age_group'],
                        'count': stat['count']
                    })
            except Exception as e:
                print(f"Error calculating age groups: {e}")
                # Fallback with mock data
                age_groups = [
                    {'name': 'Under 18', 'count': 45},
                    {'name': '18-30', 'count': 120},
                    {'name': '31-50', 'count': 98},
                    {'name': '51-65', 'count': 67},
                    {'name': 'Over 65', 'count': 38},
                ]

            # 6. ID Status Summary
            id_status_summary = []
            try:
                status_stats = IDCard.objects.values('status').annotate(count=Count('id'))
                for stat in status_stats:
                    id_status_summary.append({
                        'name': stat['status'].upper().replace('_', ' '),
                        'value': stat['count']
                    })
            except Exception as e:
                print(f"Error calculating ID status: {e}")

            # 7. New Registrations this month
            new_registrations_this_month = Citizen.objects.filter(
                created_at__gte=now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            ).count()

            # 8. Expiring IDs in next 30 days
            expiring_ids_next_30_days = IDCard.objects.filter(
                expiry_date__lte=thirty_days_from_now,
                expiry_date__gte=now.date(),
                status__in=[IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED]
            ).count()

            # 9. IDs expired over 30 days
            expired_ids_over_30_days = IDCard.objects.filter(
                expiry_date__lt=thirty_days_ago.date(),
                status__in=[IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED]
            ).count()

            # 10. Migration (Into/Out of this Kebele) - Mock data for now
            migration_data = {
                'into': 12,  # Citizens who moved into this kebele
                'outOf': 8   # Citizens who moved out of this kebele
            }

            # 11. Disability Status Distribution
            disability_distribution = []
            try:
                # Get all citizens and their disability values
                all_citizens = Citizen.objects.all()
                print(f"🔍 Total citizens for disability analysis: {all_citizens.count()}")

                # Check what disability values exist
                disability_values = Citizen.objects.values_list('disability', flat=True).distinct()
                print(f"🔍 Unique disability values in database: {list(disability_values)}")

                disability_stats = Citizen.objects.values('disability').annotate(count=Count('id'))
                print(f"🔍 Disability stats raw data: {list(disability_stats)}")

                for stat in disability_stats:
                    disability_value = stat['disability'] or 'none'
                    count = int(stat['count'])  # Ensure it's an integer

                    # Convert disability values to readable names
                    disability_name_map = {
                        'none': 'None',
                        'physical': 'Physical Disability',
                        'visual': 'Visual Impairment',
                        'hearing': 'Hearing Impairment',
                        'intellectual': 'Intellectual Disability',
                        'mental': 'Mental Health Condition',
                        'multiple': 'Multiple Disabilities',
                        'other': 'Other'
                    }

                    readable_name = disability_name_map.get(disability_value, disability_value.title() if disability_value else 'None')
                    disability_distribution.append({
                        'name': readable_name,
                        'count': count
                    })
                    print(f"✅ Added disability: {readable_name} with count: {count}")

                print(f"🔍 Final disability_distribution: {disability_distribution}")
            except Exception as e:
                print(f"❌ Error calculating disability distribution: {e}")
                import traceback
                print(f"❌ Traceback: {traceback.format_exc()}")
                # Fallback with mock data
                disability_distribution = [
                    {'name': 'None', 'count': 280},
                    {'name': 'Physical Disability', 'count': 45},
                    {'name': 'Visual Impairment', 'count': 12},
                    {'name': 'Hearing Impairment', 'count': 8},
                    {'name': 'Other', 'count': 5},
                ]

            # 12. Flagged ID Cases (incomplete, suspect duplicates, deceased)
            flagged_cases = 0
            try:
                # Count incomplete cases (citizens without ID cards)
                citizens_without_ids = Citizen.objects.filter(id_cards__isnull=True).count()

                # Count rejected ID cards
                rejected_ids = IDCard.objects.filter(status=IDCardStatus.REJECTED).count()

                flagged_cases = citizens_without_ids + rejected_ids
            except Exception as e:
                print(f"Error calculating flagged cases: {e}")

            # Compile all reports
            reports = {
                'total_citizens': total_citizens,
                'population_by_ketena': population_by_ketena,
                'top_ketenas': top_ketenas,
                'gender_ratio': {
                    'male': male_count,
                    'female': female_count,
                    'total': total_citizens
                },
                'age_group_distribution': age_groups,
                'disability_distribution': disability_distribution,  # ✅ Added disability statistics
                'id_status_summary': id_status_summary,
                'new_registrations_this_month': new_registrations_this_month,
                'expiring_ids_next_30_days': expiring_ids_next_30_days,
                'expired_ids_over_30_days': expired_ids_over_30_days,
                'migration_data': migration_data,
                'flagged_cases': flagged_cases,
            }

            return Response(reports)

    @action(detail=False, methods=['get'], url_path='subcity-dashboard/reports')
    def subcity_dashboard_reports(self, request, *args, **kwargs):
        """Get comprehensive dashboard reports for subcity-level monitoring by aggregating data from child kebeles."""
        tenant = self.get_tenant()

        # Check if current tenant is a subcity
        if tenant.type != 'subcity':
            return Response(
                {'error': 'This endpoint is only available for subcity tenants'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get all child kebele tenants
        child_kebeles = Tenant.objects.filter(parent=tenant, type='kebele')

        if not child_kebeles.exists():
            return Response({
                'error': 'No kebele tenants found under this subcity',
                'total_citizens': 0,
                'population_by_kebele': [],
                'top_kebeles': [],
                'top_ketenas': [],
                'gender_ratio': {'male': 0, 'female': 0, 'total': 0},
                'age_group_distribution': [],
                'id_status_summary': [],
                'new_registrations_this_month': 0,
                'expiring_ids_next_30_days': 0,
                'expired_ids_over_30_days': 0,
                'migration_data': {'into': 0, 'outOf': 0, 'net_migration': 0},
                'monthly_trends': [],
                'tenant_info': {
                    'name': tenant.name,
                    'type': tenant.type,
                    'child_kebeles_count': 0
                },
                'last_updated': timezone.now().isoformat()
            })

        # Current date for calculations
        now = timezone.now()
        thirty_days_ago = now - timedelta(days=30)
        thirty_days_from_now = now + timedelta(days=30)

        # Initialize aggregated data
        total_citizens = 0
        total_male = 0
        total_female = 0
        total_new_registrations = 0
        total_expiring_ids = 0
        total_expired_ids = 0
        population_by_kebele = []
        age_groups_aggregated = {}
        id_status_aggregated = {}
        all_ketenas = []

        # Collect data from all child kebeles
        for kebele in child_kebeles:
            try:
                with schema_context(kebele.schema_name):
                    from citizens.models import Citizen
                    from idcards.models import IDCard, IDCardStatus

                    # 1. Citizens count for this kebele
                    kebele_citizens = Citizen.objects.count()
                    total_citizens += kebele_citizens

                    # 2. Gender distribution for this kebele
                    kebele_male = Citizen.objects.filter(gender='male').count()
                    kebele_female = Citizen.objects.filter(gender='female').count()
                    total_male += kebele_male
                    total_female += kebele_female

                    # 3. New registrations this month for this kebele
                    kebele_new_registrations = Citizen.objects.filter(
                        created_at__gte=now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                    ).count()
                    total_new_registrations += kebele_new_registrations

                    # 4. Expiring IDs in next 30 days for this kebele
                    kebele_expiring_ids = IDCard.objects.filter(
                        expiry_date__lte=thirty_days_from_now,
                        expiry_date__gte=now.date(),
                        status__in=[IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED]
                    ).count()
                    total_expiring_ids += kebele_expiring_ids

                    # 5. IDs expired over 30 days for this kebele
                    kebele_expired_ids = IDCard.objects.filter(
                        expiry_date__lt=thirty_days_ago.date(),
                        status__in=[IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED]
                    ).count()
                    total_expired_ids += kebele_expired_ids

                    # 6. Age group distribution for this kebele
                    try:
                        age_group_stats = Citizen.objects.extra(
                            select={
                                'age_group': """
                                CASE
                                    WHEN EXTRACT(year FROM AGE(date_of_birth)) < 18 THEN 'Under 18'
                                    WHEN EXTRACT(year FROM AGE(date_of_birth)) BETWEEN 18 AND 30 THEN '18-30'
                                    WHEN EXTRACT(year FROM AGE(date_of_birth)) BETWEEN 31 AND 50 THEN '31-50'
                                    WHEN EXTRACT(year FROM AGE(date_of_birth)) BETWEEN 51 AND 65 THEN '51-65'
                                    ELSE 'Over 65'
                                END
                                """
                            }
                        ).values('age_group').annotate(count=Count('id'))

                        for stat in age_group_stats:
                            age_group = stat['age_group']
                            count = stat['count']
                            age_groups_aggregated[age_group] = age_groups_aggregated.get(age_group, 0) + count
                    except Exception as e:
                        print(f"Error calculating age groups for kebele {kebele.name}: {e}")

                    # 7. ID status summary for this kebele
                    try:
                        status_stats = IDCard.objects.values('status').annotate(count=Count('id'))
                        for stat in status_stats:
                            status_name = stat['status']
                            count = stat['count']
                            id_status_aggregated[status_name] = id_status_aggregated.get(status_name, 0) + count
                    except Exception as e:
                        print(f"Error calculating ID status for kebele {kebele.name}: {e}")

                    # 8. Ketena population for this kebele
                    try:
                        ketena_stats = Citizen.objects.values('ketena').annotate(
                            population=Count('id')
                        ).order_by('-population')

                        for stat in ketena_stats:
                            if stat['ketena']:
                                try:
                                    from shared.models import Ketena
                                    ketena_obj = Ketena.objects.get(id=stat['ketena'])
                                    all_ketenas.append({
                                        'name': f"{ketena_obj.name} ({kebele.name})",
                                        'population': stat['population'],
                                        'kebele': kebele.name
                                    })
                                except Ketena.DoesNotExist:
                                    all_ketenas.append({
                                        'name': f"Ketena {stat['ketena']} ({kebele.name})",
                                        'population': stat['population'],
                                        'kebele': kebele.name
                                    })
                    except Exception as e:
                        print(f"Error calculating ketena population for kebele {kebele.name}: {e}")

                    # Add kebele breakdown data
                    population_by_kebele.append({
                        'name': kebele.name,
                        'population': kebele_citizens,
                        'male': kebele_male,
                        'female': kebele_female,
                        'new_registrations': kebele_new_registrations,
                        'expiring_ids': kebele_expiring_ids,
                        'expired_ids': kebele_expired_ids
                    })

            except Exception as e:
                print(f"Error processing kebele {kebele.name}: {e}")
                # Add empty data for this kebele to maintain consistency
                population_by_kebele.append({
                    'name': kebele.name,
                    'population': 0,
                    'male': 0,
                    'female': 0,
                    'new_registrations': 0,
                    'expiring_ids': 0,
                    'expired_ids': 0
                })

        # Sort and get top kebeles and ketenas
        population_by_kebele.sort(key=lambda x: x['population'], reverse=True)
        top_kebeles = population_by_kebele[:3]

        all_ketenas.sort(key=lambda x: x['population'], reverse=True)
        top_ketenas = all_ketenas[:5]

        # Format aggregated data
        age_group_distribution = [
            {'name': age_group, 'count': count}
            for age_group, count in age_groups_aggregated.items()
        ]

        id_status_summary = [
            {
                'name': status.upper().replace('_', ' '),
                'value': count
            }
            for status, count in id_status_aggregated.items()
        ]

        # Generate monthly trends (mock data for now - would need historical data)
        monthly_trends = []
        for i in range(12):
            month_date = now - timedelta(days=30 * i)
            monthly_trends.append({
                'month': month_date.strftime('%b %Y'),
                'registrations': max(0, total_new_registrations - (i * 5)),
                'renewals': max(0, total_expiring_ids - (i * 3))
            })
        monthly_trends.reverse()

        # Mock migration data (would need to be implemented based on actual migration tracking)
        migration_data = {
            'into': total_new_registrations // 4,
            'outOf': total_new_registrations // 6,
            'net_migration': (total_new_registrations // 4) - (total_new_registrations // 6)
        }

        # Compile all reports
        reports = {
            'total_citizens': total_citizens,
            'population_by_kebele': population_by_kebele,
            'top_kebeles': top_kebeles,
            'top_ketenas': top_ketenas,
            'gender_ratio': {
                'male': total_male,
                'female': total_female,
                'total': total_citizens
            },
            'age_group_distribution': age_group_distribution,
            'id_status_summary': id_status_summary,
            'new_registrations_this_month': total_new_registrations,
            'expiring_ids_next_30_days': total_expiring_ids,
            'expired_ids_over_30_days': total_expired_ids,
            'migration_data': migration_data,
            'monthly_trends': monthly_trends,
            'tenant_info': {
                'name': tenant.name,
                'type': tenant.type,
                'child_kebeles_count': child_kebeles.count()
            },
            'last_updated': now.isoformat()
        }

        return Response(reports)

    @action(detail=False, methods=['get'], url_path='city-dashboard/reports')
    def city_dashboard_reports(self, request, *args, **kwargs):
        """Get comprehensive dashboard reports for city-level monitoring by aggregating data from all subcities and kebeles."""
        tenant = self.get_tenant()

        # Check if current tenant is a city
        if tenant.type != 'city':
            return Response(
                {'error': 'This endpoint is only available for city tenants'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get all child subcity tenants
        child_subcities = Tenant.objects.filter(parent=tenant, type='subcity')

        if not child_subcities.exists():
            return Response({
                'error': 'No subcity tenants found under this city',
                'total_citizens_18_plus': 0,
                'population_by_subcity': [],
                'top_subcities': [],
                'top_kebeles_citywide': [],
                'gender_ratio': {'male': 0, 'female': 0, 'total': 0},
                'age_group_distribution': [],
                'id_status_summary': [],
                'new_registrations_this_month': 0,
                'expiring_ids_next_30_days': 0,
                'expired_ids_over_30_days': 0,
                'migration_data': {'into': 0, 'outOf': 0, 'net_migration': 0},
                'monthly_trends': [],
                'tenant_info': {
                    'name': tenant.name,
                    'type': tenant.type,
                    'child_subcities_count': 0,
                    'total_kebeles_count': 0
                },
                'last_updated': timezone.now().isoformat()
            })

        # Current date for calculations
        now = timezone.now()
        thirty_days_ago = now - timedelta(days=30)
        thirty_days_from_now = now + timedelta(days=30)

        # Initialize aggregated data
        total_citizens_18_plus = 0
        total_male = 0
        total_female = 0
        total_new_registrations = 0
        total_expiring_ids = 0
        total_expired_ids = 0
        population_by_subcity = []
        age_groups_aggregated = {}
        id_status_aggregated = {}
        all_kebeles_citywide = []
        total_kebeles_count = 0

        # Collect data from all subcities and their kebeles
        for subcity in child_subcities:
            try:
                # Get all kebeles under this subcity
                subcity_kebeles = Tenant.objects.filter(parent=subcity, type='kebele')
                total_kebeles_count += subcity_kebeles.count()

                # Initialize subcity totals
                subcity_citizens_18_plus = 0
                subcity_male = 0
                subcity_female = 0
                subcity_new_registrations = 0
                subcity_expiring_ids = 0
                subcity_expired_ids = 0

                # Collect data from all kebeles under this subcity
                for kebele in subcity_kebeles:
                    try:
                        with schema_context(kebele.schema_name):
                            from citizens.models import Citizen
                            from idcards.models import IDCard, IDCardStatus

                            # 1. Citizens 18+ count for this kebele
                            kebele_citizens_18_plus = Citizen.objects.extra(
                                where=["EXTRACT(year FROM AGE(date_of_birth)) >= 18"]
                            ).count()
                            subcity_citizens_18_plus += kebele_citizens_18_plus
                            total_citizens_18_plus += kebele_citizens_18_plus

                            # 2. Gender distribution for citizens 18+ in this kebele
                            kebele_male_18_plus = Citizen.objects.filter(gender='male').extra(
                                where=["EXTRACT(year FROM AGE(date_of_birth)) >= 18"]
                            ).count()
                            kebele_female_18_plus = Citizen.objects.filter(gender='female').extra(
                                where=["EXTRACT(year FROM AGE(date_of_birth)) >= 18"]
                            ).count()
                            subcity_male += kebele_male_18_plus
                            subcity_female += kebele_female_18_plus
                            total_male += kebele_male_18_plus
                            total_female += kebele_female_18_plus

                            # 3. New registrations this month for citizens 18+ in this kebele
                            kebele_new_registrations = Citizen.objects.filter(
                                created_at__gte=now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                            ).extra(
                                where=["EXTRACT(year FROM AGE(date_of_birth)) >= 18"]
                            ).count()
                            subcity_new_registrations += kebele_new_registrations
                            total_new_registrations += kebele_new_registrations

                            # 4. Expiring IDs in next 30 days for citizens 18+ in this kebele
                            kebele_expiring_ids = IDCard.objects.filter(
                                expiry_date__lte=thirty_days_from_now,
                                expiry_date__gte=now.date(),
                                status__in=[IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED],
                                citizen__in=Citizen.objects.extra(
                                    where=["EXTRACT(year FROM AGE(date_of_birth)) >= 18"]
                                )
                            ).count()
                            subcity_expiring_ids += kebele_expiring_ids
                            total_expiring_ids += kebele_expiring_ids

                            # 5. IDs expired over 30 days for citizens 18+ in this kebele
                            kebele_expired_ids = IDCard.objects.filter(
                                expiry_date__lt=thirty_days_ago.date(),
                                status__in=[IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED],
                                citizen__in=Citizen.objects.extra(
                                    where=["EXTRACT(year FROM AGE(date_of_birth)) >= 18"]
                                )
                            ).count()
                            subcity_expired_ids += kebele_expired_ids
                            total_expired_ids += kebele_expired_ids

                            # 6. Age group distribution for citizens 18+ in this kebele
                            try:
                                age_group_stats = Citizen.objects.extra(
                                    select={
                                        'age_group': """
                                        CASE
                                            WHEN EXTRACT(year FROM AGE(date_of_birth)) BETWEEN 18 AND 24 THEN '18-24'
                                            WHEN EXTRACT(year FROM AGE(date_of_birth)) BETWEEN 25 AND 34 THEN '25-34'
                                            WHEN EXTRACT(year FROM AGE(date_of_birth)) BETWEEN 35 AND 44 THEN '35-44'
                                            WHEN EXTRACT(year FROM AGE(date_of_birth)) BETWEEN 45 AND 59 THEN '45-59'
                                            WHEN EXTRACT(year FROM AGE(date_of_birth)) >= 60 THEN '60+'
                                            ELSE 'Under 18'
                                        END
                                        """
                                    },
                                    where=["EXTRACT(year FROM AGE(date_of_birth)) >= 18"]
                                ).values('age_group').annotate(count=Count('id'))

                                for stat in age_group_stats:
                                    age_group = stat['age_group']
                                    if age_group != 'Under 18':  # Only include 18+ age groups
                                        count = stat['count']
                                        age_groups_aggregated[age_group] = age_groups_aggregated.get(age_group, 0) + count
                            except Exception as e:
                                print(f"Error calculating age groups for kebele {kebele.name}: {e}")

                            # 7. ID status summary for citizens 18+ in this kebele
                            try:
                                status_stats = IDCard.objects.filter(
                                    citizen__in=Citizen.objects.extra(
                                        where=["EXTRACT(year FROM AGE(date_of_birth)) >= 18"]
                                    )
                                ).values('status').annotate(count=Count('id'))
                                for stat in status_stats:
                                    status_name = stat['status']
                                    count = stat['count']
                                    id_status_aggregated[status_name] = id_status_aggregated.get(status_name, 0) + count
                            except Exception as e:
                                print(f"Error calculating ID status for kebele {kebele.name}: {e}")

                            # 8. Add kebele to citywide list for top kebeles ranking
                            all_kebeles_citywide.append({
                                'name': f"{kebele.name} ({subcity.name})",
                                'population': kebele_citizens_18_plus,
                                'subcity': subcity.name,
                                'kebele_name': kebele.name
                            })

                    except Exception as e:
                        print(f"Error processing kebele {kebele.name} under subcity {subcity.name}: {e}")

                # Add subcity breakdown data
                population_by_subcity.append({
                    'name': subcity.name,
                    'population': subcity_citizens_18_plus,
                    'male': subcity_male,
                    'female': subcity_female,
                    'new_registrations': subcity_new_registrations,
                    'expiring_ids': subcity_expiring_ids,
                    'expired_ids': subcity_expired_ids,
                    'kebeles_count': subcity_kebeles.count()
                })

            except Exception as e:
                print(f"Error processing subcity {subcity.name}: {e}")
                # Add empty data for this subcity to maintain consistency
                population_by_subcity.append({
                    'name': subcity.name,
                    'population': 0,
                    'male': 0,
                    'female': 0,
                    'new_registrations': 0,
                    'expiring_ids': 0,
                    'expired_ids': 0,
                    'kebeles_count': 0
                })

        # Sort and get top subcities and kebeles
        population_by_subcity.sort(key=lambda x: x['population'], reverse=True)
        top_subcities = population_by_subcity[:5]

        all_kebeles_citywide.sort(key=lambda x: x['population'], reverse=True)
        top_kebeles_citywide = all_kebeles_citywide[:10]

        # Format aggregated data
        age_group_distribution = [
            {'name': age_group, 'count': count}
            for age_group, count in age_groups_aggregated.items()
        ]

        id_status_summary = [
            {
                'name': status.upper().replace('_', ' '),
                'value': count
            }
            for status, count in id_status_aggregated.items()
        ]

        # Generate monthly trends (mock data for now - would need historical data)
        monthly_trends = []
        for i in range(12):
            month_date = now - timedelta(days=30 * i)
            monthly_trends.append({
                'month': month_date.strftime('%b %Y'),
                'registrations': max(0, total_new_registrations - (i * 8)),
                'renewals': max(0, total_expiring_ids - (i * 5))
            })
        monthly_trends.reverse()

        # Mock migration data (would need to be implemented based on actual migration tracking)
        migration_data = {
            'into': total_new_registrations // 3,
            'outOf': total_new_registrations // 5,
            'net_migration': (total_new_registrations // 3) - (total_new_registrations // 5)
        }

        # Compile all reports
        reports = {
            'total_citizens_18_plus': total_citizens_18_plus,
            'population_by_subcity': population_by_subcity,
            'top_subcities': top_subcities,
            'top_kebeles_citywide': top_kebeles_citywide,
            'gender_ratio': {
                'male': total_male,
                'female': total_female,
                'total': total_citizens_18_plus
            },
            'age_group_distribution': age_group_distribution,
            'id_status_summary': id_status_summary,
            'new_registrations_this_month': total_new_registrations,
            'expiring_ids_next_30_days': total_expiring_ids,
            'expired_ids_over_30_days': total_expired_ids,
            'migration_data': migration_data,
            'monthly_trends': monthly_trends,
            'tenant_info': {
                'name': tenant.name,
                'type': tenant.type,
                'child_subcities_count': child_subcities.count(),
                'total_kebeles_count': total_kebeles_count
            },
            'last_updated': now.isoformat()
        }

        return Response(reports)



    @action(detail=True, methods=['get'])
    def spouse(self, request, tenant_id=None, pk=None):
        """Get spouse information for a specific citizen."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen, Spouse

            citizen = get_object_or_404(Citizen, pk=pk)
            try:
                spouse = Spouse.objects.get(citizen=citizen)
                serializer = SpouseSerializer(spouse)
                return Response(serializer.data)
            except Spouse.DoesNotExist:
                return Response(None, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['get'])
    def parents(self, request, tenant_id=None, pk=None):
        """Get parents information for a specific citizen."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen, Parent

            citizen = get_object_or_404(Citizen, pk=pk)
            parents = Parent.objects.filter(citizen=citizen)
            serializer = ParentSerializer(parents, many=True)
            return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def children(self, request, tenant_id=None, pk=None):
        """Get children information for a specific citizen."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen, Child

            citizen = get_object_or_404(Citizen, pk=pk)
            children = Child.objects.filter(citizen=citizen)
            serializer = ChildSerializer(children, many=True)
            return Response(serializer.data)

    @action(detail=True, methods=['get'], url_path='emergency-contacts')
    def emergency_contacts(self, request, tenant_id=None, pk=None):
        """Get emergency contacts for a specific citizen."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen, EmergencyContact

            citizen = get_object_or_404(Citizen, pk=pk)
            contacts = EmergencyContact.objects.filter(citizen=citizen)
            serializer = EmergencyContactSerializer(contacts, many=True)

            # Expand relationship field for each contact
            contacts_data = serializer.data
            for contact_data in contacts_data:
                if contact_data.get('relationship'):
                    try:
                        from shared.models import Relationship
                        relationship = Relationship.objects.get(id=contact_data['relationship'])
                        contact_data['relationship'] = {'id': relationship.id, 'name': relationship.name}
                        print(f"🔍 Expanded emergency contact relationship: {contact_data['relationship']}")
                    except Relationship.DoesNotExist:
                        print(f"❌ Relationship with ID {contact_data['relationship']} not found")
                        pass

            return Response(contacts_data)

    @action(detail=False, methods=['get'])
    def cross_tenant_list(self, request, tenant_id=None):
        """
        Get citizens from all child tenants for subcity admins.
        This allows subcity admins to see citizens from all their kebele tenants.
        """
        # Check if user is subcity admin
        if request.user.role not in ['subcity_admin', 'superadmin'] and not request.user.is_superuser:
            return Response(
                {'error': 'Only subcity admins can view cross-tenant citizens'},
                status=status.HTTP_403_FORBIDDEN
            )

        tenant = self.get_tenant()

        # Check if current tenant is a subcity
        if tenant.type != 'subcity':
            return Response(
                {'error': 'This endpoint is only available for subcity tenants'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get pagination parameters
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 10))
        search = request.GET.get('search', '')

        # Get all child kebele tenants
        child_kebeles = Tenant.objects.filter(parent=tenant, type='kebele')

        all_citizens = []

        # Collect citizens from all child kebeles
        for kebele in child_kebeles:
            with schema_context(kebele.schema_name):
                from citizens.models import Citizen
                from idcards.models import IDCard, IDCardStatus

                # Only show citizens who have ID cards approved by kebele leader or higher
                kebele_approved_idcard_citizen_ids = IDCard.objects.filter(
                    status__in=[IDCardStatus.KEBELE_APPROVED, IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED]
                ).values_list('citizen_id', flat=True)

                queryset = Citizen.objects.filter(id__in=kebele_approved_idcard_citizen_ids)

                # Apply search filter
                if search:
                    queryset = queryset.filter(
                        Q(first_name__icontains=search) |
                        Q(middle_name__icontains=search) |
                        Q(last_name__icontains=search) |
                        Q(digital_id__icontains=search) |
                        Q(phone__icontains=search) |
                        Q(email__icontains=search)
                    )

                # Add tenant info to each citizen
                for citizen in queryset:
                    citizen_data = self.get_serializer(citizen).data
                    citizen_data['kebele_tenant'] = {
                        'id': kebele.id,
                        'name': kebele.name,
                        'schema_name': kebele.schema_name
                    }
                    all_citizens.append(citizen_data)

        # Sort by created_at descending
        all_citizens.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        # Apply pagination
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        paginated_citizens = all_citizens[start_index:end_index]

        return Response({
            'count': len(all_citizens),
            'results': paginated_citizens,
            'page': page,
            'page_size': page_size,
            'total_pages': (len(all_citizens) + page_size - 1) // page_size
        })

    def city_citizen_book(self, request, tenant_id=None):
        """
        Get citizen data from all child subcities and kebeles for city admins.
        Simple citizen list for the citizen book.
        """
        print(f"🔍 city_citizen_book method called! tenant_id: {tenant_id}")

        # Check if user is city admin
        if request.user.role not in ['city_admin', 'superadmin'] and not request.user.is_superuser:
            return Response(
                {'error': 'Only city admins can view city-wide citizen book'},
                status=status.HTTP_403_FORBIDDEN
            )

        tenant = self.get_tenant()
        print(f"🔍 Tenant: {tenant.name} (Type: {tenant.type})")

        # Check if current tenant is a city
        if tenant.type != 'city':
            return Response(
                {'error': 'This endpoint is only available for city tenants'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get search parameters
        search = request.GET.get('search', '')

        all_citizens = []
        total_count = 0

        # Get all child subcity tenants
        child_subcities = Tenant.objects.filter(parent=tenant, type='subcity')
        print(f"🔍 Found {child_subcities.count()} subcities")

        # Collect citizens from all subcities and their kebeles
        for subcity in child_subcities:
            print(f"🔍 Processing subcity: {subcity.name}")

            # Get all kebeles under this subcity
            subcity_kebeles = Tenant.objects.filter(parent=subcity, type='kebele')
            print(f"🔍 Found {subcity_kebeles.count()} kebeles in {subcity.name}")

            for kebele in subcity_kebeles:
                print(f"🔍 Processing kebele: {kebele.name}")
                try:
                    with schema_context(kebele.schema_name):
                        from citizens.models import Citizen

                        # Get all citizens
                        citizens_queryset = Citizen.objects.all()

                        # Apply search filter if provided
                        if search:
                            citizens_queryset = citizens_queryset.filter(
                                Q(first_name__icontains=search) |
                                Q(middle_name__icontains=search) |
                                Q(last_name__icontains=search) |
                                Q(digital_id__icontains=search) |
                                Q(phone__icontains=search)
                            )

                        kebele_count = citizens_queryset.count()
                        total_count += kebele_count
                        print(f"🔍 Found {kebele_count} citizens in {kebele.name}")

                        # Collect basic citizen data
                        for citizen in citizens_queryset:
                            # Get photo data
                            photo_url = None
                            photo_base64 = None

                            # Check for base64 photo first
                            if citizen.photo:
                                photo_base64 = citizen.photo

                            # Check for photo record (ImageField)
                            try:
                                if hasattr(citizen, 'photo_record') and citizen.photo_record and citizen.photo_record.photo:
                                    photo_url = citizen.photo_record.photo.url
                            except:
                                pass

                            citizen_data = {
                                'id': citizen.id,
                                'digital_id': citizen.digital_id,
                                'first_name': citizen.first_name,
                                'middle_name': citizen.middle_name,
                                'last_name': citizen.last_name,
                                'full_name': f"{citizen.first_name} {citizen.middle_name or ''} {citizen.last_name}".strip(),
                                'gender': citizen.gender,
                                'date_of_birth': citizen.date_of_birth,
                                'phone': citizen.phone,
                                'photo_base64': photo_base64,
                                'photo_url': photo_url,
                                'kebele_name': kebele.name,
                                'subcity_name': subcity.name,
                                'city_name': tenant.name
                            }
                            all_citizens.append(citizen_data)

                except Exception as e:
                    print(f"❌ Error processing kebele {kebele.name}: {e}")
                    continue

        # Sort citizens by name
        all_citizens.sort(key=lambda x: x.get('full_name', ''))

        print(f"🔍 Total citizens collected: {len(all_citizens)}")

        # Return simple citizen list
        return Response({
            'success': True,
            'city_info': {
                'id': tenant.id,
                'name': tenant.name,
                'type': tenant.type
            },
            'statistics': {
                'total_citizens': total_count,
                'total_subcities': child_subcities.count()
            },
            'citizens': all_citizens,
            'count': len(all_citizens)
        })

        tenant = self.get_tenant()

        # Check if current tenant is a city
        if tenant.type != 'city':
            return Response(
                {'error': 'This endpoint is only available for city tenants'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Current date for calculations
        now = timezone.now()

        # Get pagination and filter parameters
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        search = request.GET.get('search', '')
        subcity_filter = request.GET.get('subcity', '')
        kebele_filter = request.GET.get('kebele', '')
        gender_filter = request.GET.get('gender', '')
        age_group_filter = request.GET.get('age_group', '')
        is_book_format = request.GET.get('format') == 'book'

        # Get all child subcity tenants
        child_subcities = Tenant.objects.filter(parent=tenant, type='subcity')

        if subcity_filter:
            try:
                child_subcities = child_subcities.filter(id=int(subcity_filter))
            except ValueError:
                child_subcities = child_subcities.filter(name__icontains=subcity_filter)

        all_citizens = []
        subcity_summary = []
        total_statistics = {
            'total_citizens': 0,
            'total_male': 0,
            'total_female': 0,
            'total_subcities': 0,
            'total_kebeles': 0,
            'age_groups': {},
            'id_status_summary': {}
        }

        # Collect citizens from all subcities and their kebeles
        for subcity in child_subcities:
            # Get all kebeles under this subcity
            subcity_kebeles = Tenant.objects.filter(parent=subcity, type='kebele')

            if kebele_filter:
                try:
                    subcity_kebeles = subcity_kebeles.filter(id=int(kebele_filter))
                except ValueError:
                    subcity_kebeles = subcity_kebeles.filter(name__icontains=kebele_filter)

            subcity_citizens_count = 0
            subcity_male_count = 0
            subcity_female_count = 0
            kebele_list = []

            for kebele in subcity_kebeles:
                try:
                    with schema_context(kebele.schema_name):
                        from citizens.models import Citizen

                        # Get all citizens
                        citizens_queryset = Citizen.objects.all()

                        # Apply search filter if provided
                        if search:
                            citizens_queryset = citizens_queryset.filter(
                                Q(first_name__icontains=search) |
                                Q(middle_name__icontains=search) |
                                Q(last_name__icontains=search) |
                                Q(digital_id__icontains=search) |
                                Q(phone__icontains=search)
                            )

                        # Apply gender filter if provided
                        if gender_filter:
                            citizens_queryset = citizens_queryset.filter(gender=gender_filter)

                        # Apply age group filter if provided
                        if age_group_filter:
                            if age_group_filter == '18-30':
                                citizens_queryset = citizens_queryset.extra(
                                    where=["EXTRACT(year FROM AGE(date_of_birth)) BETWEEN 18 AND 30"]
                                )
                            elif age_group_filter == '31-50':
                                citizens_queryset = citizens_queryset.extra(
                                    where=["EXTRACT(year FROM AGE(date_of_birth)) BETWEEN 31 AND 50"]
                                )
                            elif age_group_filter == '51+':
                                citizens_queryset = citizens_queryset.extra(
                                    where=["EXTRACT(year FROM AGE(date_of_birth)) >= 51"]
                                )

                        kebele_citizens_count = citizens_queryset.count()
                        kebele_male_count = citizens_queryset.filter(gender='male').count()
                        kebele_female_count = citizens_queryset.filter(gender='female').count()

                        subcity_citizens_count += kebele_citizens_count
                        subcity_male_count += kebele_male_count
                        subcity_female_count += kebele_female_count

                        # Add to totals
                        total_statistics['total_citizens'] += kebele_citizens_count
                        total_statistics['total_male'] += kebele_male_count
                        total_statistics['total_female'] += kebele_female_count

                        # Collect basic citizen data only
                        for citizen in citizens_queryset:
                            # Calculate age (basic info)
                            age = None
                            if citizen.date_of_birth:
                                from datetime import date
                                today = date.today()
                                age = today.year - citizen.date_of_birth.year - ((today.month, today.day) < (citizen.date_of_birth.month, citizen.date_of_birth.day))

                            # Basic citizen data only
                            citizen_data = {
                                'id': citizen.id,
                                'digital_id': citizen.digital_id,
                                'first_name': citizen.first_name,
                                'middle_name': citizen.middle_name,
                                'last_name': citizen.last_name,
                                'full_name': f"{citizen.first_name} {citizen.middle_name or ''} {citizen.last_name}".strip(),
                                'gender': citizen.gender,
                                'date_of_birth': citizen.date_of_birth,
                                'age': age,
                                'phone': citizen.phone,
                                'kebele': {
                                    'id': kebele.id,
                                    'name': kebele.name
                                },
                                'subcity': {
                                    'id': subcity.id,
                                    'name': subcity.name
                                },
                                'city': {
                                    'id': tenant.id,
                                    'name': tenant.name
                                }
                            }
                            all_citizens.append(citizen_data)

                        kebele_list.append({
                            'id': kebele.id,
                            'name': kebele.name,
                            'citizens_count': kebele_citizens_count,
                            'male_count': kebele_male_count,
                            'female_count': kebele_female_count
                        })

                except Exception as e:
                    print(f"Error processing kebele {kebele.name} under subcity {subcity.name}: {e}")
                    continue

            total_statistics['total_kebeles'] += len(kebele_list)

            subcity_summary.append({
                'id': subcity.id,
                'name': subcity.name,
                'citizens_count': subcity_citizens_count,
                'male_count': subcity_male_count,
                'female_count': subcity_female_count,
                'kebeles_count': len(kebele_list),
                'kebeles': kebele_list
            })

        total_statistics['total_subcities'] = len(subcity_summary)

        # Sort citizens by name
        all_citizens.sort(key=lambda x: x.get('full_name', ''))

        if is_book_format:
            # For book format, return all citizens organized by subcity and kebele
            book_data = {
                'city_info': {
                    'id': tenant.id,
                    'name': tenant.name,
                    'type': tenant.type,
                    'generated_date': now.strftime('%B %d, %Y'),
                    'generated_time': now.strftime('%I:%M %p')
                },
                'statistics': total_statistics,
                'subcity_directory': []
            }

            # Organize citizens by subcity and kebele for book format
            for subcity_data in subcity_summary:
                subcity_entry = {
                    'subcity_name': subcity_data['name'],
                    'subcity_stats': {
                        'total_citizens': subcity_data['citizens_count'],
                        'male_count': subcity_data['male_count'],
                        'female_count': subcity_data['female_count'],
                        'kebeles_count': subcity_data['kebeles_count']
                    },
                    'kebeles': []
                }

                # Get citizens for each kebele in this subcity
                for kebele_data in subcity_data['kebeles']:
                    kebele_citizens = [
                        citizen for citizen in all_citizens
                        if citizen['subcity']['id'] == subcity_data['id'] and
                           citizen['kebele']['id'] == kebele_data['id']
                    ]

                    # Sort citizens alphabetically by full name
                    kebele_citizens.sort(key=lambda x: x['full_name'])

                    kebele_entry = {
                        'kebele_name': kebele_data['name'],
                        'kebele_stats': {
                            'total_citizens': len(kebele_citizens),
                            'male_count': len([c for c in kebele_citizens if c['gender'] == 'male']),
                            'female_count': len([c for c in kebele_citizens if c['gender'] == 'female'])
                        },
                        'citizens': kebele_citizens
                    }

                    subcity_entry['kebeles'].append(kebele_entry)

                # Sort kebeles alphabetically
                subcity_entry['kebeles'].sort(key=lambda x: x['kebele_name'])
                book_data['subcity_directory'].append(subcity_entry)

            # Sort subcities alphabetically
            book_data['subcity_directory'].sort(key=lambda x: x['subcity_name'])

            return Response(book_data)

        else:
            # Apply pagination
            start_index = (page - 1) * page_size
            end_index = start_index + page_size
            paginated_citizens = all_citizens[start_index:end_index]

            # Regular paginated response
            response_data = {
                'count': len(all_citizens),
                'results': paginated_citizens,
                'page': page,
                'page_size': page_size,
                'total_pages': (len(all_citizens) + page_size - 1) // page_size,
                'city_info': {
                    'id': tenant.id,
                    'name': tenant.name,
                    'type': tenant.type
                },
                'statistics': total_statistics,
                'subcity_summary': subcity_summary,
                'filters': {
                    'search': search,
                    'subcity': subcity_filter,
                    'kebele': kebele_filter,
                    'gender': gender_filter,
                    'age_group': age_group_filter
                }
            }
            return Response(response_data)

    @action(detail=False, methods=['get'])
    def city_citizen_list(self, request, tenant_id=None):
        """
        Get citizens from all child subcities and kebeles for city admins.
        This allows city admins to see citizens from all subcities and kebeles under their jurisdiction.
        """
        # Check if user is city admin
        if request.user.role not in ['city_admin', 'superadmin'] and not request.user.is_superuser:
            return Response(
                {'error': 'Only city admins can view city-wide citizens'},
                status=status.HTTP_403_FORBIDDEN
            )

        tenant = self.get_tenant()

        # Check if current tenant is a city
        if tenant.type != 'city':
            return Response(
                {'error': 'This endpoint is only available for city tenants'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get pagination parameters
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 10))
        search = request.GET.get('search', '')
        subcity_filter = request.GET.get('subcity', '')
        kebele_filter = request.GET.get('kebele', '')

        # Get all child subcity tenants
        child_subcities = Tenant.objects.filter(parent=tenant, type='subcity')

        if subcity_filter:
            child_subcities = child_subcities.filter(id=subcity_filter)

        all_citizens = []
        subcity_info = []

        # Collect citizens from all subcities and their kebeles
        for subcity in child_subcities:
            # Get all kebeles under this subcity
            subcity_kebeles = Tenant.objects.filter(parent=subcity, type='kebele')

            if kebele_filter:
                subcity_kebeles = subcity_kebeles.filter(id=kebele_filter)

            # Add subcity info for filtering
            subcity_info.append({
                'id': subcity.id,
                'name': subcity.name,
                'kebeles': [{'id': k.id, 'name': k.name} for k in subcity_kebeles]
            })

            for kebele in subcity_kebeles:
                try:
                    with schema_context(kebele.schema_name):
                        from citizens.models import Citizen
                        from idcards.models import IDCard, IDCardStatus

                        # Only show citizens who have ID cards approved by kebele leader or higher
                        kebele_approved_idcard_citizen_ids = IDCard.objects.filter(
                            status__in=[IDCardStatus.KEBELE_APPROVED, IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED]
                        ).values_list('citizen_id', flat=True)

                        queryset = Citizen.objects.filter(id__in=kebele_approved_idcard_citizen_ids)

                        # Apply search filter
                        if search:
                            queryset = queryset.filter(
                                Q(first_name__icontains=search) |
                                Q(middle_name__icontains=search) |
                                Q(last_name__icontains=search) |
                                Q(digital_id__icontains=search) |
                                Q(phone__icontains=search) |
                                Q(email__icontains=search)
                            )

                        # Add tenant info to each citizen
                        for citizen in queryset:
                            citizen_data = self.get_serializer(citizen).data
                            citizen_data['kebele_tenant'] = {
                                'id': kebele.id,
                                'name': kebele.name,
                                'schema_name': kebele.schema_name
                            }
                            citizen_data['subcity_tenant'] = {
                                'id': subcity.id,
                                'name': subcity.name,
                                'schema_name': subcity.schema_name
                            }
                            citizen_data['city_tenant'] = {
                                'id': tenant.id,
                                'name': tenant.name,
                                'schema_name': tenant.schema_name
                            }
                            all_citizens.append(citizen_data)

                except Exception as e:
                    print(f"Error processing kebele {kebele.name} under subcity {subcity.name}: {e}")
                    continue

        # Sort by created_at descending
        all_citizens.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        # Apply pagination
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        paginated_citizens = all_citizens[start_index:end_index]

        return Response({
            'count': len(all_citizens),
            'results': paginated_citizens,
            'page': page,
            'page_size': page_size,
            'total_pages': (len(all_citizens) + page_size - 1) // page_size,
            'subcities': subcity_info,
            'city_info': {
                'id': tenant.id,
                'name': tenant.name,
                'type': tenant.type,
                'total_subcities': child_subcities.count(),
                'total_kebeles': sum(len(sc['kebeles']) for sc in subcity_info)
            }
        })


class TenantSpecificEmergencyContactViewSet(viewsets.ModelViewSet):
    """API endpoint for managing emergency contacts within a specific tenant schema."""
    serializer_class = EmergencyContactSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageCitizens]

    def get_tenant(self):
        """Get the tenant from URL parameter."""
        tenant_id = self.kwargs.get('tenant_id')
        return get_object_or_404(Tenant, id=tenant_id)

    def get_queryset(self):
        """Get emergency contacts from the specific tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import EmergencyContact
            return EmergencyContact.objects.all()


class TenantSpecificParentViewSet(viewsets.ModelViewSet):
    """API endpoint for managing parents within a specific tenant schema."""
    serializer_class = ParentSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageCitizens]

    def get_tenant(self):
        """Get the tenant from URL parameter."""
        tenant_id = self.kwargs.get('tenant_id')
        return get_object_or_404(Tenant, id=tenant_id)

    def get_queryset(self):
        """Get parents from the specific tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Parent
            return Parent.objects.all()


class TenantSpecificChildViewSet(viewsets.ModelViewSet):
    """API endpoint for managing children within a specific tenant schema."""
    serializer_class = ChildSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageCitizens]

    def get_tenant(self):
        """Get the tenant from URL parameter."""
        tenant_id = self.kwargs.get('tenant_id')
        return get_object_or_404(Tenant, id=tenant_id)

    def get_queryset(self):
        """Get children from the specific tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Child
            return Child.objects.all()


class TenantSpecificSpouseViewSet(viewsets.ModelViewSet):
    """API endpoint for managing spouses within a specific tenant schema."""
    serializer_class = SpouseSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageCitizens]

    def get_tenant(self):
        """Get the tenant from URL parameter."""
        tenant_id = self.kwargs.get('tenant_id')
        return get_object_or_404(Tenant, id=tenant_id)

    def get_queryset(self):
        """Get spouses from the specific tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Spouse
            return Spouse.objects.all()


class TenantSpecificDocumentViewSet(viewsets.ModelViewSet):
    """API endpoint for managing documents within a specific tenant schema."""
    serializer_class = DocumentSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageCitizens]

    def get_tenant(self):
        """Get the tenant from URL parameter."""
        tenant_id = self.kwargs.get('tenant_id')
        return get_object_or_404(Tenant, id=tenant_id)

    def get_queryset(self):
        """Get documents from the specific tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from tenants.models.citizen import Document
            queryset = Document.objects.select_related('citizen', 'document_type').all()

            # Filter by citizen if provided
            citizen_id = self.request.query_params.get('citizen')
            if citizen_id:
                queryset = queryset.filter(citizen_id=citizen_id)

            return queryset.order_by('-created_at')

    def list(self, request, *args, **kwargs):
        """List documents in the tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        """Create a new document in the tenant schema."""
        print(f"🔍 TenantSpecificDocumentViewSet.create called")
        print(f"🔍 Request data: {request.data}")
        print(f"🔍 Request files: {request.FILES}")
        print(f"🔍 Tenant ID from URL: {self.kwargs.get('tenant_id')}")

        tenant = self.get_tenant()
        print(f"🔍 Tenant: {tenant.name} (Schema: {tenant.schema_name})")

        with schema_context(tenant.schema_name):
            print(f"🔍 Inside tenant schema context: {tenant.schema_name}")
            serializer = self.get_serializer(data=request.data)
            print(f"🔍 Serializer created, validating...")

            try:
                serializer.is_valid(raise_exception=True)
                print(f"🔍 Serializer is valid, saving...")
                result = serializer.save()
                print(f"🔍 Document saved successfully: {result}")
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            except Exception as e:
                print(f"❌ Error in create: {str(e)}")
                print(f"❌ Serializer errors: {getattr(serializer, 'errors', 'No errors available')}")
                import traceback
                print(f"❌ Traceback: {traceback.format_exc()}")
                raise

    def retrieve(self, request, *args, **kwargs):
        """Retrieve a specific document from the tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        """Update a document in the tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            partial = kwargs.pop('partial', False)
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=partial)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        """Delete a document from the tenant schema."""
        print(f"🔍 TenantSpecificDocumentViewSet.destroy called!")
        print(f"🔍 Request method: {request.method}")
        print(f"🔍 Request path: {request.path}")
        print(f"🔍 kwargs: {kwargs}")

        tenant = self.get_tenant()
        document_id = kwargs.get('pk')

        print(f"🔍 Attempting to delete document {document_id} from tenant {tenant.id} ({tenant.schema_name})")
        print(f"🔍 User: {request.user.email} (role: {request.user.role})")

        with schema_context(tenant.schema_name):
            try:
                from tenants.models.citizen import Document

                # Debug: Check all documents in this tenant schema
                all_docs = Document.objects.all()
                print(f"🔍 All documents in tenant {tenant.schema_name}: {list(all_docs.values_list('id', 'document_type__name', 'citizen__id'))}")

                # Check if document exists
                if not Document.objects.filter(id=document_id).exists():
                    print(f"❌ Document {document_id} not found in tenant schema {tenant.schema_name}")

                    # Check in other schemas to see where it might be
                    from tenants.models import Tenant
                    for other_tenant in Tenant.objects.all():
                        if other_tenant.id != tenant.id:
                            with schema_context(other_tenant.schema_name):
                                if Document.objects.filter(id=document_id).exists():
                                    print(f"🔍 Found document {document_id} in different tenant schema: {other_tenant.schema_name}")
                                    break

                    return Response(
                        {'error': f'Document {document_id} not found in tenant {tenant.name}'},
                        status=status.HTTP_404_NOT_FOUND
                    )

                instance = self.get_object()
                print(f"🔍 Found document: {instance}")
                print(f"🔍 Document citizen: {instance.citizen}")
                print(f"🔍 Document file: {instance.document_file}")

                instance.delete()
                print(f"✅ Document {document_id} deleted successfully")
                return Response(status=status.HTTP_204_NO_CONTENT)

            except Exception as e:
                print(f"❌ Error deleting document: {str(e)}")
                import traceback
                print(f"❌ Traceback: {traceback.format_exc()}")
                return Response(
                    {'error': f'Failed to delete document: {str(e)}'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
