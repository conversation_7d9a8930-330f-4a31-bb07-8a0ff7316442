import React, { useState, useEffect } from 'react';
import { Box, Typography, Avatar } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import axios from '../../utils/axios';

/**
 * Simple QR Code component using QR Server API
 */
const QRCode = ({ value, size = 80, level = "M" }) => {
  const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(value)}`;

  return (
    <img
      src={qrUrl}
      alt="QR Code"
      style={{
        width: size,
        height: size
      }}
      onError={(e) => {
        // Fallback to placeholder if QR service fails
        e.target.style.display = 'none';
        if (e.target.nextSibling) {
          e.target.nextSibling.style.display = 'flex';
        }
      }}
    />
  );
};

const IDCardTemplate = ({ idCard, side = 'front', preview = false }) => {
  const { user } = useAuth();
  const [tenantNames, setTenantNames] = useState({
    city_name_am: user?.city_tenant_name || 'ጎንደር',
    subcity_name_am: user?.parent_tenant_name || 'ዞብል',
    kebele_name_am: user?.tenant_name || 'ገብርኤል'
  });
  const [mayorSignature, setMayorSignature] = useState(null);
  const [tenantLogo, setTenantLogo] = useState(null);
  const [subcityPatternImage, setSubcityPatternImage] = useState(null);

  // Get city name from token for microprint
  const getCityName = () => {
    // Get city name from user token (parent of subcity)
    return user?.city_tenant_name || user?.parent_tenant_name || 'GONDAR';
  };

  // Generate random positions for microprint text
  const generateMicroprintPositions = () => {
    const positions = [];
    const cityName = getCityName();

    // Create a grid-based approach for better distribution
    const gridSize = 3; // 3x3 grid for 9 positions
    const cellWidth = 80 / gridSize;
    const cellHeight = 80 / gridSize;

    for (let i = 0; i < 9; i++) {
      const row = Math.floor(i / gridSize);
      const col = i % gridSize;

      // Add some randomness within each grid cell
      const baseTop = row * cellHeight + 10;
      const baseLeft = col * cellWidth + 10;

      positions.push({
        id: i,
        text: cityName,
        top: baseTop + Math.random() * (cellHeight * 0.8), // Random within cell
        left: baseLeft + Math.random() * (cellWidth * 0.8), // Random within cell
        rotation: Math.random() * 360, // Random rotation
        opacity: 0.3 + Math.random() * 0.2 // 0.3 to 0.5 opacity for better visibility
      });
    }
    return positions;
  };

  const microprintPositions = generateMicroprintPositions();

  // Determine current user's tenant type for display logic
  const getCurrentTenantType = () => {
    let currentTenantType = user?.tenant_type || user?.tenant?.type;

    if (!currentTenantType) {
      if (user?.role === 'subcity_admin' || user?.role === 'subcity_clerk') {
        currentTenantType = 'subcity';
      } else if (user?.role === 'city_admin' || user?.role === 'city_clerk') {
        currentTenantType = 'city';
      } else {
        currentTenantType = 'kebele';
      }
    }

    return currentTenantType;
  };

  const currentTenantType = getCurrentTenantType();

  if (!idCard) {
    return (
      <Box
        sx={{
          width: '100%',
          height: '300px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '1px dashed #ccc',
          borderRadius: 2
        }}
      >
        <Typography color="text.secondary">No ID card data</Typography>
      </Box>
    );
  }

  const citizen = idCard.citizen || {};
  const tenantHierarchy = idCard.tenant_hierarchy || {};

  // Set tenant names from token and fetch Amharic names
  useEffect(() => {
    const setTenantData = async () => {
      try {
        console.log('🔍 Setting tenant data from token:', {
          cityTenantId: user?.city_tenant_id,
          cityTenantName: user?.city_tenant_name,
          parentTenantId: user?.parent_tenant_id,
          parentTenantName: user?.parent_tenant_name,
          tenantId: user?.tenant_id,
          tenantName: user?.tenant_name,
          tenantType: user?.tenant_type
        });

        // Set initial names from token as fallback
        setTenantNames(prev => ({
          ...prev,
          city_name_am: user?.city_tenant_name || 'ጎንደር',
          subcity_name_am: user?.parent_tenant_name || 'ዞብል',
          kebele_name_am: user?.tenant_name || 'ገብርኤል'
        }));

        // Use Amharic names directly from JWT token database values
        console.log('🔍 Setting tenant names using database values from JWT token...');
        console.log('🔍 User tenant type:', user?.tenant_type);
        console.log('🔍 User token data:', {
          tenant_name_am: user?.tenant_name_am,
          parent_tenant_name_am: user?.parent_tenant_name_am,
          city_tenant_name_am: user?.city_tenant_name_am
        });
        console.log('🔍 Full user object:', user);
        console.log('🔍 Citizen data:', citizen);
        console.log('🔍 ID card data:', idCard);

        // Helper function to get Amharic name with intelligent fallbacks
        const getAmharicName = (amharicName, englishName, tenantType = 'unknown') => {
          // First priority: Use Amharic name from database (via JWT token)
          if (amharicName && amharicName !== null && amharicName.trim() !== '') {
            return amharicName;
          }

          // Second priority: Intelligent conversion for numbered kebeles
          if (englishName?.toLowerCase().startsWith('kebele')) {
            const number = englishName.replace(/[^0-9]/g, '');
            return number ? `ቀበሌ ${number}` : englishName;
          }

          // Third priority: Smart conversion for common English names
          const nameConversions = {
            'gondar': 'ጎንደር',
            'zoble': 'ዞብል',
            'gabriel': 'ገብርኤል',
            'kebele14': 'ቀበሌ 14',
            'kebele 14': 'ቀበሌ 14',
            'city': 'ከተማ',
            'subcity': 'ክ/ከተማ',
            'kebele': 'ቀበሌ'
          };

          const lowerEnglishName = englishName?.toLowerCase();
          if (lowerEnglishName && nameConversions[lowerEnglishName]) {
            return nameConversions[lowerEnglishName];
          }

          // Fourth priority: Type-based defaults with English name
          const defaults = {
            'city': englishName ? `${englishName} ከተማ` : 'ጎንደር ከተማ',
            'subcity': englishName ? `${englishName} ክ/ከተማ` : 'ዞብል ክ/ከተማ',
            'kebele': englishName ? `${englishName} ቀበሌ` : 'ቀበሌ 14',
            'unknown': englishName || 'አስተዳደር'
          };

          // Fifth priority: English name as fallback
          return defaults[tenantType] || englishName || defaults.unknown;
        };

        let cityNameAm, subcityNameAm, kebeleNameAm;

        if (user?.tenant_type === 'kebele') {
          // In kebele context: Use logged-in user's tenant hierarchy
          console.log('🔍 Kebele context: Using user tenant info');
          cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
          subcityNameAm = getAmharicName(user?.parent_tenant_name_am, user?.parent_tenant_name, 'subcity');
          kebeleNameAm = getAmharicName(user?.tenant_name_am, user?.tenant_name, 'kebele');

        } else if (user?.tenant_type === 'subcity') {
          // In subcity context: Use citizen's original kebele info
          console.log('🔍 Subcity context: Using citizen kebele info');

          // Get kebele tenant info from ID card data or citizen data
          const kebeleInfo = idCard?.kebele_tenant;
          const kebeleId = kebeleInfo?.id || citizen?.kebele;

          console.log('🔍 Kebele info from ID card:', kebeleInfo);
          console.log('🔍 Kebele ID for fetching:', kebeleId);

          // Use city info from logged-in user (subcity admin)
          cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');

          // Use subcity info from logged-in user (subcity admin)
          subcityNameAm = getAmharicName(user?.tenant_name_am, user?.tenant_name, 'subcity');

          // Fetch kebele Amharic name from database
          if (kebeleId) {
            try {
              console.log(`🔍 Fetching kebele tenant data for Amharic name, kebele ID: ${kebeleId}`);
              const kebeleResponse = await axios.get(`/api/tenants/${kebeleId}/`);
              const kebeleData = kebeleResponse.data;

              console.log('🔍 Kebele tenant data for Amharic name:', kebeleData);

              // Get Amharic name from kebele profile data
              if (kebeleData.profile_data?.name_am) {
                kebeleNameAm = kebeleData.profile_data.name_am;
                console.log('🔍 Kebele Amharic name from profile_data:', kebeleNameAm);
              } else if (kebeleData.name_am) {
                kebeleNameAm = kebeleData.name_am;
                console.log('🔍 Kebele Amharic name from tenant data:', kebeleNameAm);
              } else {
                // Fallback: use intelligent conversion
                kebeleNameAm = getAmharicName(null, kebeleData.name || kebeleInfo?.name, 'kebele');
                console.log('🔍 Kebele Amharic name from intelligent conversion:', kebeleNameAm);
              }
            } catch (error) {
              console.error('❌ Error fetching kebele Amharic name:', error);
              // Fallback: use intelligent conversion with available data
              kebeleNameAm = getAmharicName(null, kebeleInfo?.name, 'kebele');
            }
          } else {
            // Fallback: use default kebele name
            kebeleNameAm = getAmharicName(null, null, 'kebele');
          }

        } else if (user?.tenant_type === 'city') {
          // In city context: Use logged-in user's tenant info
          console.log('🔍 City context: Using user tenant info');
          cityNameAm = getAmharicName(user?.tenant_name_am, user?.tenant_name, 'city');

          // For city level, we might need to get subcity/kebele from citizen data
          if (citizen?.subcity || citizen?.kebele) {
            // TODO: Fetch subcity/kebele names from citizen data
            subcityNameAm = getAmharicName(null, null, 'subcity');
            kebeleNameAm = getAmharicName(null, null, 'kebele');
          } else {
            subcityNameAm = getAmharicName(null, null, 'subcity');
            kebeleNameAm = getAmharicName(null, null, 'kebele');
          }

        } else {
          // Default fallback for other tenant types (superadmin, etc.)
          console.log('🔍 Other context: Using intelligent defaults');
          cityNameAm = getAmharicName(user?.city_tenant_name_am, user?.city_tenant_name, 'city');
          subcityNameAm = getAmharicName(user?.parent_tenant_name_am, user?.parent_tenant_name, 'subcity');
          kebeleNameAm = getAmharicName(user?.tenant_name_am, user?.tenant_name, 'kebele');
        }

        console.log('🔍 Final Amharic name mapping (from database):', {
          city: `${user?.city_tenant_name || 'N/A'} → ${cityNameAm} (DB: ${user?.city_tenant_name_am || 'None'})`,
          subcity: `${user?.parent_tenant_name || user?.tenant_name || 'N/A'} → ${subcityNameAm} (DB: ${user?.parent_tenant_name_am || user?.tenant_name_am || 'None'})`,
          kebele: `${user?.tenant_name || idCard?.kebele_tenant?.name || 'N/A'} → ${kebeleNameAm} (DB: ${user?.tenant_name_am || 'None'})`
        });

        setTenantNames(prev => ({
          ...prev,
          city_name_am: cityNameAm,
          subcity_name_am: subcityNameAm,
          kebele_name_am: kebeleNameAm
        }));

        // Fetch tenant data including logo, signature, and pattern
        await fetchTenantData();

      } catch (error) {
        console.error('❌ Error setting tenant data:', error);
      }
    };

    const fetchTenantData = async () => {
      try {
        console.log('🔍 Fetching tenant data for ID card...');
        console.log('🔍 Citizen data:', citizen);
        console.log('🔍 User data:', user);

        // Fetch Amharic names for ID card header
        await fetchAmharicNames();

        // Fetch tenant profile data for logo, signature, and pattern
        await fetchTenantProfileData();

      } catch (error) {
        console.error('❌ Error fetching tenant data:', error);
      }
    };

    const fetchAmharicNames = async () => {
      try {
        console.log('🔍 Fetching Amharic names based on user context...');
        console.log('🔍 User tenant type:', user?.tenant_type);

        let amharicData = {};

        if (user?.tenant_type === 'kebele') {
          // For kebele users: Use token data directly
          amharicData = {
            city_name_am: user?.city_tenant_name_am || user?.city_tenant_name || 'ጎንደር',
            subcity_name_am: user?.parent_tenant_name_am || user?.parent_tenant_name || 'ዞብል',
            kebele_name_am: user?.tenant_name_am || user?.tenant_name || 'ገብርኤል'
          };
          console.log('🔍 Kebele context: Using token data directly');

        } else if (user?.tenant_type === 'subcity') {
          // For subcity users: Get city/subcity from token, fetch kebele from database
          amharicData = {
            city_name_am: user?.city_tenant_name_am || user?.city_tenant_name || 'ጎንደር',
            subcity_name_am: user?.tenant_name_am || user?.tenant_name || 'ዞብል',
            kebele_name_am: 'ገብርኤል' // Default, will be updated below
          };

          // Fetch kebele Amharic name from database
          const kebeleId = idCard?.kebele_tenant?.id || citizen?.kebele;
          if (kebeleId) {
            try {
              console.log(`🔍 Subcity context: Fetching kebele Amharic name for ID: ${kebeleId}`);
              const kebeleResponse = await axios.get(`/api/tenants/${kebeleId}/`);
              const kebeleData = kebeleResponse.data;

              // Get Amharic name from kebele data
              if (kebeleData.profile_data?.name_am) {
                amharicData.kebele_name_am = kebeleData.profile_data.name_am;
                console.log('🔍 Kebele Amharic name from profile_data:', amharicData.kebele_name_am);
              } else if (kebeleData.name_am) {
                amharicData.kebele_name_am = kebeleData.name_am;
                console.log('🔍 Kebele Amharic name from tenant data:', amharicData.kebele_name_am);
              } else {
                // Fallback: use English name or default
                amharicData.kebele_name_am = kebeleData.name || idCard?.kebele_tenant?.name || 'ገብርኤል';
                console.log('🔍 Kebele Amharic name fallback:', amharicData.kebele_name_am);
              }
            } catch (error) {
              console.error('❌ Error fetching kebele Amharic name for subcity user:', error);
              amharicData.kebele_name_am = idCard?.kebele_tenant?.name || 'ገብርኤል';
            }
          }
          console.log('🔍 Subcity context: Final Amharic data');

        } else {
          // For other users (city, superadmin): Use token data as fallback
          amharicData = {
            city_name_am: user?.city_tenant_name_am || user?.city_tenant_name || 'ጎንደር',
            subcity_name_am: user?.parent_tenant_name_am || user?.parent_tenant_name || 'ዞብል',
            kebele_name_am: user?.tenant_name_am || user?.tenant_name || 'ገብርኤል'
          };
          console.log('🔍 Other context: Using token data as fallback');
        }

        console.log('🔍 Final Amharic names:', amharicData);

        // Update tenant names with Amharic data
        setTenantNames({
          city_name_am: amharicData.city_name_am,
          subcity_name_am: amharicData.subcity_name_am,
          kebele_name_am: amharicData.kebele_name_am
        });

        console.log('🔍 Amharic names updated successfully');

      } catch (error) {
        console.error('❌ Error fetching Amharic names:', error);
      }
    };

    const fetchTenantProfileData = async () => {
      try {
        console.log('🔍 Fetching tenant profile data...');
        console.log('🔍 Current user tenant type:', user?.tenant_type);
        console.log('🔍 Current user tenant ID:', user?.tenant_id);
        console.log('🔍 Full user object:', user);

        // Get tenant type from user object or JWT token
        // Try multiple ways to determine tenant type
        let currentTenantType = user?.tenant_type || user?.tenant?.type;
        let currentTenantId = user?.tenant_id || user?.tenant?.id;

        // If tenant_type is not available, try to determine from role or other fields
        if (!currentTenantType) {
          if (user?.role === 'subcity_admin' || user?.role === 'subcity_clerk') {
            currentTenantType = 'subcity';
          } else if (user?.role === 'city_admin' || user?.role === 'city_clerk') {
            currentTenantType = 'city';
          } else {
            // Default to kebele for other roles
            currentTenantType = 'kebele';
          }
        }

        // If still no tenant ID, try to get it from parent tenant info
        if (!currentTenantId) {
          currentTenantId = user?.parent_tenant_id; // For subcity context
        }

        console.log('🔍 Determined tenant type:', currentTenantType);
        console.log('🔍 Determined tenant ID:', currentTenantId);

        // Determine which logo to use based on current user's tenant type
        if (currentTenantType === 'kebele' && currentTenantId) {
          // If current tenant is kebele, fetch kebele profile directly using tenant ID
          console.log(`🔍 Fetching kebele profile for current user's tenant ID: ${currentTenantId}`);
          try {
            // Fetch the specific tenant with profile data
            const tenantResponse = await axios.get(`/api/tenants/${currentTenantId}/`);
            console.log('🔍 Tenant API response:', tenantResponse);

            const tenantData = tenantResponse.data;
            console.log('🔍 Tenant data:', tenantData);

            // Check if tenant has kebele profile data
            console.log('🔍 Kebele profile_data contents:', tenantData.profile_data);

            if (tenantData.profile_data && tenantData.profile_data.logo) {
              // Use full backend URL for media files to avoid proxy issues
              const logoUrl = tenantData.profile_data.logo.startsWith('http')
                ? tenantData.profile_data.logo
                : `http://localhost:8000${tenantData.profile_data.logo}`;
              setTenantLogo(logoUrl);
              console.log('🔍 Current kebele logo set from profile:', logoUrl);
            } else {
              console.log('⚠️ No kebele profile logo found in tenant data');
              if (tenantData.profile_data) {
                console.log('🔍 Available profile_data fields:', Object.keys(tenantData.profile_data));
                console.log('🔍 Logo field value:', tenantData.profile_data.logo);
                console.log('🔍 Mayor signature field value:', tenantData.profile_data.mayor_signature);
                console.log('🔍 Pattern image field value:', tenantData.profile_data.pattern_image);
              } else {
                console.log('🔍 No profile_data available');
              }

              // Fallback: try to fetch kebele profile directly if it exists
              try {
                const kebeleProfileResponse = await axios.get(`/api/tenants/${currentTenantId}/kebele-profile/`);
                const kebeleProfile = kebeleProfileResponse.data;
                console.log('🔍 Kebele profile response:', kebeleProfile);

                if (kebeleProfile.logo) {
                  // Use full backend URL for media files to avoid proxy issues
                  const logoUrl = kebeleProfile.logo.startsWith('http')
                    ? kebeleProfile.logo
                    : `http://localhost:8000${kebeleProfile.logo}`;
                  setTenantLogo(logoUrl);
                  console.log('🔍 Current kebele logo set from direct profile:', logoUrl);
                }
              } catch (profileError) {
                console.log('⚠️ No direct kebele profile endpoint available');
              }
            }
          } catch (error) {
            console.error('❌ Error fetching current kebele tenant data:', error);
          }
        } else if (currentTenantType === 'subcity' && currentTenantId) {
          // If current tenant is subcity, fetch subcity profile directly using tenant ID
          console.log(`🔍 Fetching subcity profile for current user's tenant ID: ${currentTenantId}`);
          try {
            // Fetch the specific tenant with profile data
            const tenantResponse = await axios.get(`/api/tenants/${currentTenantId}/`);
            console.log('🔍 Subcity tenant API response:', tenantResponse);

            const tenantData = tenantResponse.data;
            console.log('🔍 Subcity tenant data:', tenantData);

            // Check if tenant has subcity profile data
            if (tenantData.profile_data && tenantData.profile_data.logo) {
              // Use full backend URL for media files to avoid proxy issues
              const logoUrl = tenantData.profile_data.logo.startsWith('http')
                ? tenantData.profile_data.logo
                : `http://localhost:8000${tenantData.profile_data.logo}`;
              setTenantLogo(logoUrl);
              console.log('🔍 Current subcity logo set from profile:', logoUrl);
            } else {
              console.log('⚠️ No subcity profile logo found in tenant data');
            }

            // For subcity users, also set their mayor signature and pattern image for display
            if (tenantData.profile_data) {
              if (tenantData.profile_data.mayor_signature) {
                // Use full backend URL for media files to avoid proxy issues
                const signatureUrl = tenantData.profile_data.mayor_signature.startsWith('http')
                  ? tenantData.profile_data.mayor_signature
                  : `http://localhost:8000${tenantData.profile_data.mayor_signature}`;
                setMayorSignature(signatureUrl);
                console.log('🔍 Subcity mayor signature set for display:', signatureUrl);
              }

              if (tenantData.profile_data.pattern_image) {
                // Use full backend URL for media files to avoid proxy issues
                const patternUrl = tenantData.profile_data.pattern_image.startsWith('http')
                  ? tenantData.profile_data.pattern_image
                  : `http://localhost:8000${tenantData.profile_data.pattern_image}`;
                setSubcityPatternImage(patternUrl);
                console.log('🔍 Subcity pattern image set for display:', patternUrl);
              }
            }
          } catch (error) {
            console.error('❌ Error fetching current subcity tenant data:', error);
          }
        } else {
          // Fallback: try to get kebele logo from citizen data
          if (citizen?.kebele) {
            console.log(`🔍 Fallback: Fetching kebele tenant data for citizen's kebele ID: ${citizen.kebele}`);
            try {
              // Fetch the specific citizen's kebele tenant with profile data
              const tenantResponse = await axios.get(`/api/tenants/${citizen.kebele}/`);
              console.log('🔍 Fallback kebele tenant API response:', tenantResponse);

              const tenantData = tenantResponse.data;
              console.log('🔍 Fallback kebele tenant data:', tenantData);

              // Check if tenant has kebele profile data
              console.log('🔍 Citizen kebele profile_data contents:', tenantData.profile_data);

              if (tenantData.profile_data && tenantData.profile_data.logo) {
                // Use full backend URL for media files to avoid proxy issues
                const logoUrl = tenantData.profile_data.logo.startsWith('http')
                  ? tenantData.profile_data.logo
                  : `http://localhost:8000${tenantData.profile_data.logo}`;
                setTenantLogo(logoUrl);
                console.log('🔍 Citizen kebele logo set from profile:', logoUrl);
              } else {
                console.log('⚠️ No citizen kebele profile logo found');
                console.log('🔍 Available citizen profile_data fields:', tenantData.profile_data ? Object.keys(tenantData.profile_data) : 'No profile_data');
              }
            } catch (error) {
              console.error('❌ Error fetching citizen kebele tenant data:', error);
            }
          }
        }

        // Fetch subcity tenant data for security pattern and mayor signature when approved
        if (citizen?.subcity && idCard.subcity_admin_approved) {
          console.log(`🔍 Fetching subcity tenant data for security pattern, subcity ID: ${citizen.subcity}`);
          try {
            // Fetch the specific subcity tenant with profile data
            const tenantResponse = await axios.get(`/api/tenants/${citizen.subcity}/`);
            console.log('🔍 Subcity tenant API response for security pattern:', tenantResponse);

            const tenantData = tenantResponse.data;
            console.log('🔍 Subcity tenant data for security pattern:', tenantData);

            // Check if tenant has subcity profile data
            if (tenantData.profile_data) {
              // Set mayor signature when subcity admin approved
              if (tenantData.profile_data.mayor_signature) {
                // Use full backend URL for media files to avoid proxy issues
                const signatureUrl = tenantData.profile_data.mayor_signature.startsWith('http')
                  ? tenantData.profile_data.mayor_signature
                  : `http://localhost:8000${tenantData.profile_data.mayor_signature}`;
                setMayorSignature(signatureUrl);
                console.log('🔍 Subcity mayor signature set for approved ID:', signatureUrl);
              }

              // Set pattern image when subcity admin approved
              if (tenantData.profile_data.pattern_image) {
                const patternUrl = tenantData.profile_data.pattern_image.startsWith('http')
                  ? tenantData.profile_data.pattern_image
                  : `http://localhost:8000${tenantData.profile_data.pattern_image}`;
                setSubcityPatternImage(patternUrl);
                console.log('🔍 Subcity pattern image set for approved ID:', patternUrl);
              }
            } else {
              console.log('⚠️ No subcity profile data found for security pattern');
            }
          } catch (error) {
            console.error('❌ Error fetching subcity tenant data for security pattern:', error);
          }
        }

      } catch (error) {
        console.error('❌ Error fetching tenant profile data:', error);
      }
    };

    if (user) {
      setTenantData();
    }
  }, [user, idCard, citizen, idCard.subcity_admin_approved]);

  // Debug: Log the actual data structure to help with field mapping
  console.log('🔍 ID Card Template Data Debug:', {
    idCard: idCard,
    citizen: citizen,
    tenantHierarchy: tenantHierarchy,
    tenantNames: tenantNames,
    mayorSignature: mayorSignature,
    citizenFields: {
      phone: citizen.phone,
      phone_number: citizen.phone_number,
      blood_type: citizen.blood_type,
      employment: citizen.employment,
      occupation: citizen.occupation,
      emergency_contacts: citizen.emergency_contacts,
      ketena: citizen.ketena,
      place_of_birth: citizen.place_of_birth,
      subcity: citizen.subcity,
      kebele: citizen.kebele
    }
  });

  // Additional debug for logo rendering
  console.log('🔍 Logo rendering debug:', {
    tenantLogo,
    fallbackLogo: "/images/gondar-logo.jpg",
    finalSrc: tenantLogo || "/images/gondar-logo.jpg",
    isNull: tenantLogo === null,
    isUndefined: tenantLogo === undefined,
    urlType: typeof tenantLogo,
    urlLength: tenantLogo ? tenantLogo.length : 0
  });

  // Log the logo URL for debugging
  if (tenantLogo) {
    console.log('🔍 Logo URL set:', tenantLogo);
  }

  // Standard ID card dimensions: 3.375" x 2.125" (exact measurements)
  // 3.375" = 243px at 72 DPI, 2.125" = 153px at 72 DPI
  const cardStyle = {
    width: preview ? '486px' : '243px', // 3.375" at 144 DPI for preview, 72 DPI for normal
    height: preview ? '306px' : '153px', // 2.125" at 144 DPI for preview, 72 DPI for normal
    position: 'relative',
    backgroundColor: '#ffffff',
    border: '1px solid #ddd',
    borderRadius: '6px',
    overflow: 'hidden',
    fontFamily: '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
    aspectRatio: '3.375/2.125' // Exact standard ID card ratio
  };



  if (side === 'front') {
    return (
      <Box sx={cardStyle}>

        {/* Background watermark pattern */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            opacity: 0.05,
            background: `
              radial-gradient(circle at 20% 20%, rgba(44, 62, 80, 0.1) 1px, transparent 1px),
              radial-gradient(circle at 80% 80%, rgba(44, 62, 80, 0.1) 1px, transparent 1px),
              radial-gradient(circle at 40% 60%, rgba(44, 62, 80, 0.1) 1px, transparent 1px),
              radial-gradient(circle at 60% 40%, rgba(44, 62, 80, 0.1) 1px, transparent 1px),
              linear-gradient(45deg, rgba(255, 143, 0, 0.02) 25%, transparent 25%),
              linear-gradient(-45deg, rgba(255, 143, 0, 0.02) 25%, transparent 25%)
            `,
            backgroundSize: '20px 20px, 25px 25px, 30px 30px, 35px 35px, 8px 8px, 8px 8px',
            zIndex: 1
          }}
        />

        {/* Enhanced Bahamas-style Pattern - Excluding header area */}
        <Box
          sx={{
            position: 'absolute',
            top: preview ? 120 : 90, // Start below header
            left: 0,
            right: 0,
            bottom: 0,
            width: '100%',
            height: 'calc(100% - 120px)', // Adjust height to exclude header
            opacity: 0.8, // Increased opacity for better visibility
            zIndex: 2, // Behind everything else
            background: `
              /* Base gradient wash - Light blue/teal like Bahamas license */
              linear-gradient(135deg,
                rgba(173, 216, 230, 0.6) 0%,
                rgba(135, 206, 235, 0.7) 25%,
                rgba(176, 224, 230, 0.6) 50%,
                rgba(175, 238, 238, 0.7) 75%,
                rgba(173, 216, 230, 0.6) 100%),

              /* Flowing wave patterns - Similar to Bahamas license */
              repeating-linear-gradient(
                45deg,
                rgba(0, 191, 255, 0.3) 0px,
                rgba(0, 191, 255, 0.3) 1px,
                transparent 1px,
                transparent 4px,
                rgba(135, 206, 235, 0.25) 4px,
                rgba(135, 206, 235, 0.25) 5px,
                transparent 5px,
                transparent 8px
              ),
              repeating-linear-gradient(
                -45deg,
                rgba(176, 224, 230, 0.3) 0px,
                rgba(176, 224, 230, 0.3) 1px,
                transparent 1px,
                transparent 4px,
                rgba(175, 238, 238, 0.25) 4px,
                rgba(175, 238, 238, 0.25) 5px,
                transparent 5px,
                transparent 8px
              ),

              /* Curved wave lines - Creating flowing effect */
              repeating-linear-gradient(
                30deg,
                rgba(0, 206, 209, 0.4) 0px,
                rgba(0, 206, 209, 0.4) 2px,
                transparent 2px,
                transparent 12px,
                rgba(64, 224, 208, 0.3) 12px,
                rgba(64, 224, 208, 0.3) 14px,
                transparent 14px,
                transparent 24px
              ),
              repeating-linear-gradient(
                150deg,
                rgba(72, 209, 204, 0.4) 0px,
                rgba(72, 209, 204, 0.4) 2px,
                transparent 2px,
                transparent 12px,
                rgba(95, 158, 160, 0.3) 12px,
                rgba(95, 158, 160, 0.3) 14px,
                transparent 14px,
                transparent 24px
              ),

              /* Subtle crosshatch for texture */
              repeating-linear-gradient(
                120deg,
                rgba(176, 196, 222, 0.2) 0px,
                rgba(176, 196, 222, 0.2) 1px,
                transparent 1px,
                transparent 6px
              ),
              repeating-linear-gradient(
                60deg,
                rgba(230, 230, 250, 0.2) 0px,
                rgba(230, 230, 250, 0.2) 1px,
                transparent 1px,
                transparent 6px
              ),

              /* Radial wave patterns for depth */
              radial-gradient(ellipse at 20% 30%,
                rgba(0, 191, 255, 0.3) 0%,
                rgba(135, 206, 235, 0.2) 40%,
                transparent 70%),
              radial-gradient(ellipse at 80% 70%,
                rgba(176, 224, 230, 0.3) 0%,
                rgba(175, 238, 238, 0.2) 40%,
                transparent 70%),
              radial-gradient(ellipse at 60% 20%,
                rgba(72, 209, 204, 0.25) 0%,
                rgba(64, 224, 208, 0.15) 50%,
                transparent 80%),

              /* Fine mesh pattern for security */
              repeating-conic-gradient(
                from 0deg at 50% 50%,
                rgba(0, 206, 209, 0.15) 0deg,
                rgba(0, 206, 209, 0.15) 2deg,
                transparent 2deg,
                transparent 8deg,
                rgba(135, 206, 235, 0.1) 8deg,
                rgba(135, 206, 235, 0.1) 10deg,
                transparent 10deg,
                transparent 16deg
              ),

              /* Vertical Web Pattern */
              repeating-linear-gradient(
                0deg,
                rgba(0, 150, 200, 0.08) 0px,
                rgba(0, 150, 200, 0.08) 1px,
                transparent 1px,
                transparent 3px,
                rgba(100, 180, 220, 0.06) 3px,
                rgba(100, 180, 220, 0.06) 4px,
                transparent 4px,
                transparent 8px
              ),

              /* Horizontal Web Pattern */
              repeating-linear-gradient(
                90deg,
                rgba(0, 150, 200, 0.08) 0px,
                rgba(0, 150, 200, 0.08) 1px,
                transparent 1px,
                transparent 3px,
                rgba(100, 180, 220, 0.06) 3px,
                rgba(100, 180, 220, 0.06) 4px,
                transparent 4px,
                transparent 8px
              ),

              /* Maurer Rose Pattern */
              radial-gradient(ellipse at 25% 25%,
                rgba(0, 180, 220, 0.1) 0%,
                rgba(0, 180, 220, 0.05) 20%,
                transparent 40%),
              radial-gradient(ellipse at 75% 25%,
                rgba(0, 180, 220, 0.1) 0%,
                rgba(0, 180, 220, 0.05) 20%,
                transparent 40%),
              radial-gradient(ellipse at 25% 75%,
                rgba(0, 180, 220, 0.1) 0%,
                rgba(0, 180, 220, 0.05) 20%,
                transparent 40%),
              radial-gradient(ellipse at 75% 75%,
                rgba(0, 180, 220, 0.1) 0%,
                rgba(0, 180, 220, 0.05) 20%,
                transparent 40%),

              /* Guilloche Web Pattern */
              repeating-conic-gradient(
                from 45deg at 30% 30%,
                rgba(0, 160, 200, 0.06) 0deg,
                rgba(0, 160, 200, 0.06) 5deg,
                transparent 5deg,
                transparent 15deg,
                rgba(80, 180, 220, 0.04) 15deg,
                rgba(80, 180, 220, 0.04) 20deg,
                transparent 20deg,
                transparent 30deg
              ),
              repeating-conic-gradient(
                from -45deg at 70% 70%,
                rgba(0, 160, 200, 0.06) 0deg,
                rgba(0, 160, 200, 0.06) 5deg,
                transparent 5deg,
                transparent 15deg,
                rgba(80, 180, 220, 0.04) 15deg,
                rgba(80, 180, 220, 0.04) 20deg,
                transparent 20deg,
                transparent 30deg
              )
            `,
            backgroundSize: '8px 8px, 8px 8px, 24px 24px, 24px 24px, 6px 6px, 6px 6px, 200px 150px, 200px 150px, 180px 120px, 360deg, 8px 8px, 8px 8px, 50px 50px, 50px 50px, 50px 50px, 50px 50px, 100px 100px, 100px 100px',
            pointerEvents: 'none'
          }}
        />



        {/* City Name Microprint Text - 9 random positions */}
        {microprintPositions.map((position) => (
          <Typography
            key={position.id}
            sx={{
              position: 'absolute',
              top: `${position.top}%`,
              left: `${position.left}%`,
              fontSize: preview ? '8px' : '6px', // Increased font size for visibility
              fontWeight: 'bold',
              color: '#0d47a1', // Darker blue for better visibility
              opacity: 0.4, // Increased opacity for better visibility
              transform: `rotate(${position.rotation}deg)`,
              transformOrigin: 'center',
              pointerEvents: 'none',
              zIndex: 4, // Above pattern but below main content
              fontFamily: 'monospace',
              letterSpacing: '0.3px',
              userSelect: 'none',
              textShadow: '0 0 1px rgba(255, 255, 255, 0.5)', // Add subtle shadow for contrast
              WebkitTextStroke: '0.2px rgba(13, 71, 161, 0.8)' // Add text stroke for definition
            }}
          >
            {position.text}
          </Typography>
        ))}

        {/* Subcity Pattern Image Overlay - Only on main content and footer, excluding header */}
        {(idCard.subcity_admin_approved || currentTenantType === 'subcity') && subcityPatternImage && (
          <Box
            sx={{
              position: 'absolute',
              top: preview ? 140 : 110, // Below header completely
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `url(${subcityPatternImage})`,
              backgroundSize: 'cover', // Cover the entire area
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'center',
              opacity: 0.04, // Even lighter pattern
              zIndex: 1, // Behind all content
              pointerEvents: 'none'
            }}
          />
        )}

        {/* Header with Ethiopian flag and title - Completely transparent */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            px: preview ? 1.5 : 1,
            py: preview ? 0.2 : 0.1, // Reduced margins
            backgroundColor: 'transparent', // Completely transparent to eliminate white line
            position: 'relative',
            zIndex: 10, // Higher z-index to ensure visibility over pattern
            margin: 0, // Remove any margin
            mb: preview ? 0.3 : 0.2 // Small margin bottom to move line up
          }}
        >
          {/* Golden line with star pattern as header's bottom border */}
          <Box
            sx={{
              position: 'absolute',
              bottom: preview ? -1.5 : -1, // Position line as bottom border of header
              left: 0,
              right: 0,
              height: preview ? '1.5px' : '1px', // Thicker line
              background: `
                linear-gradient(to right,
                  #DAA520 0%,
                  #DAA520 45%,
                  transparent 45%,
                  transparent 55%,
                  #DAA520 55%,
                  #DAA520 100%
                )
              `,
              zIndex: 15 // Higher z-index to be above everything
            }}
          />

          {/* Star pattern in the middle of the header's bottom border */}
          <Box
            sx={{
              position: 'absolute',
              bottom: preview ? -4 : -3, // Position star on the bottom border line
              left: '50%',
              transform: 'translateX(-50%)',
              fontSize: preview ? '12px' : '9px',
              color: '#DAA520',
              zIndex: 16, // Highest z-index
              textShadow: '0 0 2px #B8860B'
            }}
          >
            ★
          </Box>
          {/* Dynamic Tenant Logo */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ml: preview ? 1 : 0.5, mt: preview ? -0.5 : -0.3 }}>
            <Box
              sx={{
                width: preview ? 100 : 70, // 2x larger: 50*2=100, 35*2=70
                height: preview ? 50 : 35,  // 2x larger: 35*2=70, 25*2=50
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'transparent' // Make background transparent
              }}
            >
              <img
                key={tenantLogo || "/images/gondar-logo.jpg"} // Force re-render when logo changes
                src={tenantLogo || "/images/gondar-logo.jpg"}
                alt={`${user?.tenant_type || 'Tenant'} Logo`}
                crossOrigin="anonymous" // Add CORS support
                onLoad={(e) => {
                  console.log('✅ Logo loaded successfully:', tenantLogo);
                  console.log('✅ Actual loaded src:', e.target.src);
                  console.log('✅ Image dimensions:', e.target.naturalWidth, 'x', e.target.naturalHeight);
                  // Ensure the image is visible
                  e.target.style.display = 'block';
                  // Hide the placeholder
                  if (e.target.nextSibling) {
                    e.target.nextSibling.style.display = 'none';
                  }
                }}
                style={{
                  width: preview ? 100 : 70,  // Made bigger: was 100/70, now 120/85
                  height: preview ? 50 : 35,  // Made bigger: was 50/35, now 60/42
                  objectFit: 'contain',
                  display: 'block', // Ensure it's visible by default
                  backgroundColor: 'transparent', // Make logo background transparent
                  border: 'none', // Remove any border
                  boxShadow: 'none' // Remove any shadow
                }}
                onError={(e) => {
                  console.error('❌ Logo failed to load:', tenantLogo);
                  console.error('❌ Failed src:', e.target.src);
                  console.error('❌ Error details:', e.type, e.target.complete, e.target.naturalWidth);

                  // Prevent infinite loop by checking if we already tried fallback
                  if (tenantLogo && e.target.src.includes(tenantLogo)) {
                    console.log('🔄 Falling back to default logo');
                    e.target.src = "/images/gondar-logo.jpg";
                  } else {
                    // If even default logo fails, show placeholder
                    console.log('🔄 Showing logo placeholder');
                    e.target.style.display = 'none';
                    if (e.target.nextSibling) {
                      e.target.nextSibling.style.display = 'flex';
                    }
                  }
                }}
              />
              {/* Fallback placeholder */}
              <Box
                sx={{
                  width: preview ? 50 : 35,
                  height: preview ? 35 : 25,
                  backgroundColor: '#f0f0f0',
                  border: '1px solid #ccc',
                  borderRadius: 1,
                  display: 'none',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <Typography sx={{ fontSize: preview ? '8px' : '6px', color: '#666', fontWeight: 'bold' }}>
                  LOGO
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Title Section */}
          <Box sx={{ textAlign: 'center', flex: 1 }}>
            <Typography
              sx={{
                fontSize: preview ? '12px' : '9px', // Slightly smaller for two lines
                fontWeight: 'bold',
                color: '#000000', // Black color
                lineHeight: 1.1,
                margin: 0,
                padding: 0
              }}
            >
              በ{tenantNames.city_name_am} ከተማ አስተዳደር በ{tenantNames.subcity_name_am} ክ/ከተማ የ{tenantNames.kebele_name_am} ቀበሌ
            </Typography>
            <Typography
              sx={{
                fontSize: preview ? '12px' : '9px', // Same size as first line
                fontWeight: 'bold',
                color: '#000000', // Black color
                lineHeight: 1.1,
                margin: 0,
                padding: 0,
                mt: preview ? 0.1 : 0.05 // Small spacing between lines
              }}
            >
              የነዋሪዎች መታወቂያ ካርድ
            </Typography>
          </Box>
        </Box>

        {/* Main Content */}
        <Box sx={{
          display: 'flex',
          px: preview ? 1.5 : 1, // Only horizontal padding
          pt: preview ? 0.5 : 0.3, // Small top padding for spacing without white line
          pb: preview ? 3 : 2, // Extra bottom padding for footer space
          gap: preview ? 1.5 : 1,
          position: 'relative',
          zIndex: 100, // Much higher z-index to ensure all content is in front
          alignItems: 'center', // Center content vertically
          minHeight: preview ? '180px' : '100px', // Adjusted height for footer
          backgroundColor: 'transparent' // Ensure no background color
        }}>
          {/* Photo Section */}
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%'
          }}>
            <Box
              sx={{
                width: preview ? 140 : 110, // Maximized photo size for better visualization
                height: preview ? 180 : 140, // Maximized height maintaining 3:4 ratio
                overflow: 'hidden',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mb: 0, // No margin bottom - barcode will be directly underneath
                position: 'relative',
                zIndex: 100, // Much higher z-index to ensure photo is in front
                boxShadow: preview ? '0 4px 8px rgba(0, 0, 0, 0.15)' : '0 2px 4px rgba(0, 0, 0, 0.15)', // Added shadow
                borderRadius: '2px' // Slight border radius for professional look
              }}
            >
              {(citizen.photo || idCard.citizen_photo || idCard.citizen?.photo) ? (
                <img
                  src={citizen.photo || idCard.citizen_photo || idCard.citizen?.photo}
                  alt="Citizen Photo"
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    position: 'relative',
                    zIndex: 10
                  }}
                />
              ) : (
                <Typography sx={{ fontSize: preview ? '12px' : '10px', color: '#7f8c8d', fontWeight: 'bold' }}>
                  PHOTO
                </Typography>
              )}
            </Box>

            {/* Barcode directly under photo with no space */}
            <Box
              sx={{
                width: preview ? 140 : 110, // Match photo width
                height: preview ? 20 : 15,
                backgroundColor: '#fff',
                backgroundImage: `
                  repeating-linear-gradient(90deg,
                    #000 0px, #000 1px,
                    #fff 1px, #fff 2px,
                    #000 2px, #000 3px,
                    #fff 3px, #fff 5px,
                    #000 5px, #000 6px,
                    #fff 6px, #fff 7px,
                    #000 7px, #000 9px,
                    #fff 9px, #fff 10px,
                    #000 10px, #000 11px,
                    #fff 11px, #fff 13px,
                    #000 13px, #000 15px,
                    #fff 15px, #fff 16px,
                    #000 16px, #000 17px,
                    #fff 17px, #fff 18px,
                    #000 18px, #000 20px
                  )
                `
              }}
            />
            <Typography
              sx={{
                fontSize: preview ? '10px' : '8px',
                fontFamily: 'monospace',
                textAlign: 'center',
                mt: 0.3,
                fontWeight: 'bold',
                color: '#000000'
              }}
            >
              {citizen.digital_id || idCard.citizen_digital_id || idCard.card_number || '9572431481361091'}
            </Typography>



            
          </Box>

          {/* Citizen Information */}
          <Box sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            gap: preview ? 0.8 : 0.6,
            justifyContent: 'center', // Center content vertically
            position: 'relative',
            zIndex: 100 // Much higher z-index to ensure text is in front
          }}>

            {/* Name Section */}
            <Box>
              <Typography
                sx={{
                  fontSize: preview ? '10px' : '8px',
                  color: '#6C0345',  // Dark blue color
                  fontWeight: 'bold',
                  mb: 0.1
                }}
              >
                ሙሉ ስም | Fullname
              </Typography>
              <Typography
                sx={{
                  fontSize: preview ? '13px' : '10px',
                  fontWeight: 'bold',
                  color: '#000000',  // Changed to black for better print visibility
                  lineHeight: 1.1
                }}
              >
                {citizen.first_name_am && citizen.middle_name_am && citizen.last_name_am
                  ? `${citizen.first_name_am} ${citizen.middle_name_am} ${citizen.last_name_am}`
                  : idCard.citizen_name ||
                    (citizen.first_name && citizen.middle_name && citizen.last_name
                      ? `${citizen.first_name} ${citizen.middle_name} ${citizen.last_name}`
                      : 'ተዎድሮስ አበበው ቸኮል')}
              </Typography>
              <Typography
                sx={{
                  fontSize: preview ? '10px' : '8px',
                  color: '#000000',  // Changed to black for better print visibility
                  fontWeight: 'bold'  // Made bold
                }}
              >
                {citizen.first_name && citizen.middle_name && citizen.last_name
                  ? `${citizen.first_name} ${citizen.middle_name} ${citizen.last_name}`
                  : idCard.citizen_name || 'Tewodros Abebaw Chekol'}
              </Typography>
            </Box>

            {/* Date of Birth */}
            <Box>
              <Typography
                sx={{
                  fontSize: preview ? '11px' : '9px',
                  color: '#6C0345',  // Dark blue color
                  fontWeight: 'bold',
                  mb: 0.1
                }}
              >
                የትውልድ ቀን | Date of Birth
              </Typography>
              <Typography
                sx={{
                  fontSize: preview ? '13px' : '10px',
                  fontWeight: 'bold',
                  color: '#000000'  // Changed to black for better print visibility
                }}
              >
                {citizen.date_of_birth_ethiopian ||
                 idCard.citizen_date_of_birth_ethiopian ||
                 (citizen.date_of_birth || idCard.citizen_date_of_birth ?
                   new Date(citizen.date_of_birth || idCard.citizen_date_of_birth).toLocaleDateString('en-GB') :
                   '23/08/1980')}
              </Typography>
              <Typography
                sx={{
                  fontSize: preview ? '13px' : '10px',
                  color: '#000000',  // Changed to black for better print visibility
                  fontWeight: 'bold'  // Made bold
                }}
              >
                {citizen.date_of_birth || idCard.citizen_date_of_birth ?
                  new Date(citizen.date_of_birth || idCard.citizen_date_of_birth).toLocaleDateString('en-CA') :
                  '1988-05-01'}
              </Typography>
            </Box>

            {/* Gender */}
            <Box>
              <Typography
                sx={{
                  fontSize: preview ? '11px' : '9px',
                  color: '#6C0345',  // Dark blue color
                  fontWeight: 'bold',
                  mb: 0.1
                }}
              >
                ጾታ | Sex
              </Typography>
              <Typography
                sx={{
                  fontSize: preview ? '13px' : '10px',
                  fontWeight: 'bold',
                  color: '#000000'  // Changed to black for better print visibility
                }}
              >
                {(citizen.gender || idCard.citizen_gender) === 'M' ||
                 (citizen.gender || idCard.citizen_gender) === 'male' ? 'ወንድ' :
                 (citizen.gender || idCard.citizen_gender) === 'F' ||
                 (citizen.gender || idCard.citizen_gender) === 'female' ? 'ሴት' : 'ወንድ'}
              </Typography>
              <Typography
                sx={{
                  fontSize: preview ? '13px' : '10px',
                  color: '#000000',  // Changed to black for better print visibility
                  fontWeight: 'bold'  // Made bold
                }}
              >
                {(citizen.gender || idCard.citizen_gender) === 'M' ||
                 (citizen.gender || idCard.citizen_gender) === 'male' ? 'Male' :
                 (citizen.gender || idCard.citizen_gender) === 'F' ||
                 (citizen.gender || idCard.citizen_gender) === 'female' ? 'Female' : 'Male'}
              </Typography>
            </Box>

            {/* Nationality */}
            <Box>
              <Typography
                sx={{
                  fontSize: preview ? '11px' : '9px',
                  color: '#091057',  // Dark blue color
                  fontWeight: 'bold',
                  mb: 0.1
                }}
              >
                ዜግነት | Nationality
              </Typography>
              <Typography
                sx={{
                  fontSize: preview ? '13px' : '10px',
                  fontWeight: 'bold',
                  color: '#000000'  // Changed to black for better print visibility
                }}
              >
                ኢትዮጵያዊ | Ethiopian
              </Typography>
              {/* <Typography
                sx={{
                  fontSize: preview ? '12px' : '10px',
                  color: '#000000',  // Changed to black for better print visibility
                  fontWeight: 'bold'  // Made bold
                }}
              >
                
              </Typography> */}
            </Box>
          </Box>

        </Box>

        {/* Signature Section - Closer to right bottom corner - Show when subcity admin approved OR for subcity users */}
        {(idCard.subcity_admin_approved || currentTenantType === 'subcity') && mayorSignature && (
          <Box
            sx={{
              position: 'absolute',
              bottom: preview ? 15 : 10, // Closer to bottom corner
              right: preview ? 2 : 1, // Even closer to right corner
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              zIndex: 3
            }}
          >
            <Box
              sx={{
                width: preview ? 180 : 135, // 3x larger: 60*3=180, 45*3=135
                height: preview ? 90 : 66,  // 3x larger: 30*3=90, 22*3=66
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'transparent', // Make signature background transparent
                mb: 0.1
              }}
            >
              <img
                src={mayorSignature}
                alt="Mayor Signature"
                style={{
                  width: preview ? 180 : 135,
                  height: preview ? 90 : 66,
                  objectFit: 'contain',
                  opacity: 0.9, // Make signature transparent like the sample
                  filter: 'contrast(1.3) brightness(0.8)' // Enhance contrast for better visibility
                }}
                onError={(e) => {
                  // Fallback to placeholder if signature fails to load
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
              {/* Fallback placeholder */}
              <Typography sx={{
                fontSize: preview ? '8px' : '6px',
                textAlign: 'center',
                color: '#7f8c8d',
                fontWeight: 'bold',
                display: 'none'
              }}>
                MAYOR SIGNATURE
              </Typography>
            </Box>

            <Typography
              sx={{
                fontSize: preview ? '13px' : '11px',
                fontWeight: 'bold',
                color: '#09122C',
                textAlign: 'center'
              }}
            >
              ፊርማ | Signature
            </Typography>
          </Box>
        )}



        {/* Footer with Issue/Expiry Dates - More transparent to show geometric pattern */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            backgroundColor: 'rgba(248, 249, 250, 0.75)', // More transparent to show pattern clearly
            borderTop: '1px solid rgba(233, 236, 239, 0.8)',
            px: preview ? 1.5 : 1,
            py: preview ? 0.4 : 0.3,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 10, // Higher z-index to ensure visibility over pattern
            backdropFilter: 'blur(0.5px)' // Minimal blur to maintain pattern visibility
          }}
        >
          <Typography
            sx={{
              fontSize: preview ? '10px' : '8px',
              color: '#2c3e50',
              fontWeight: 'bold',
              letterSpacing: '0.2px',
              textAlign: 'center'
            }}
          >
            ISSUE/የተሰጠበት: {idCard.issue_date ? new Date(idCard.issue_date).toLocaleDateString('en-GB') : new Date().toLocaleDateString('en-GB')} | EXPIRY/የሚያበቃበት: {idCard.expiry_date ? new Date(idCard.expiry_date).toLocaleDateString('en-GB') : new Date(new Date().setFullYear(new Date().getFullYear() + 10)).toLocaleDateString('en-GB')}
          </Typography>
        </Box>
      </Box>
    );
  } else {
    // Back side
    return (
      <Box sx={cardStyle}>

        {/* Background watermark pattern */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            opacity: 0.05,
            background: `
              radial-gradient(circle at 20% 20%, rgba(44, 62, 80, 0.1) 1px, transparent 1px),
              radial-gradient(circle at 80% 80%, rgba(44, 62, 80, 0.1) 1px, transparent 1px),
              radial-gradient(circle at 40% 60%, rgba(44, 62, 80, 0.1) 1px, transparent 1px),
              radial-gradient(circle at 60% 40%, rgba(44, 62, 80, 0.1) 1px, transparent 1px),
              linear-gradient(45deg, rgba(255, 143, 0, 0.02) 25%, transparent 25%),
              linear-gradient(-45deg, rgba(255, 143, 0, 0.02) 25%, transparent 25%)
            `,
            backgroundSize: '20px 20px, 25px 25px, 30px 30px, 35px 35px, 8px 8px, 8px 8px',
            zIndex: 1
          }}
        />

        {/* Enhanced Bahamas-style Pattern - Back side full coverage */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            width: '100%',
            height: '100%',
            opacity: 0.7, // Slightly reduced for back side
            zIndex: 1, // Behind everything else
            background: `
              /* Base gradient wash - Light blue/teal like front side */
              linear-gradient(-135deg,
                rgba(172, 197, 206, 0.5) 0%,
                rgba(132, 137, 139, 0.6) 25%,
                rgba(176, 224, 230, 0.5) 50%,
                rgba(175, 238, 238, 0.6) 75%,
                rgba(173, 216, 230, 0.5) 100%),

              /* Flowing wave patterns - Mirrored from front */
              repeating-linear-gradient(
                -45deg,
                rgba(207, 216, 219, 0.25) 0px,
                rgba(97, 140, 155, 0.25) 1px,
                transparent 1px,
                transparent 4px,
                rgba(135, 206, 235, 0.2) 4px,
                rgba(135, 206, 235, 0.2) 5px,
                transparent 5px,
                transparent 8px
              ),
              repeating-linear-gradient(
                45deg,
                rgba(103, 107, 107, 0.25) 0px,
                rgba(176, 224, 230, 0.25) 1px,
                transparent 1px,
                transparent 4px,
                rgba(175, 238, 238, 0.2) 4px,
                rgba(164, 183, 183, 0.2) 5px,
                transparent 5px,
                transparent 8px
              ),

              /* Curved wave lines - Different angles for back */
              repeating-linear-gradient(
                -30deg,
                rgba(107, 136, 137, 0.3) 0px,
                rgba(0, 206, 209, 0.3) 2px,
                transparent 2px,
                transparent 12px,
                rgba(64, 224, 208, 0.25) 12px,
                rgba(64, 224, 208, 0.25) 14px,
                transparent 14px,
                transparent 24px
              ),
              repeating-linear-gradient(
                -150deg,
                rgba(141, 156, 156, 0.3) 0px,
                rgba(72, 209, 204, 0.3) 2px,
                transparent 2px,
                transparent 12px,
                rgba(95, 158, 160, 0.25) 12px,
                rgba(95, 158, 160, 0.25) 14px,
                transparent 14px,
                transparent 24px
              ),

              /* Subtle crosshatch for texture */
              repeating-linear-gradient(
                -120deg,
                rgba(176, 196, 222, 0.15) 0px,
                rgba(176, 196, 222, 0.15) 1px,
                transparent 1px,
                transparent 6px
              ),
              repeating-linear-gradient(
                -60deg,
                rgba(230, 230, 250, 0.15) 0px,
                rgba(230, 230, 250, 0.15) 1px,
                transparent 1px,
                transparent 6px
              ),

              /* Radial wave patterns for depth - Mirrored positions */
              radial-gradient(ellipse at 80% 70%,
                rgba(0, 191, 255, 0.25) 0%,
                rgba(135, 206, 235, 0.15) 40%,
                transparent 70%),
              radial-gradient(ellipse at 20% 30%,
                rgba(176, 224, 230, 0.25) 0%,
                rgba(175, 238, 238, 0.15) 40%,
                transparent 70%),
              radial-gradient(ellipse at 40% 80%,
                rgba(72, 209, 204, 0.2) 0%,
                rgba(64, 224, 208, 0.1) 50%,
                transparent 80%),

              /* Fine mesh pattern for security */
              repeating-conic-gradient(
                from 180deg at 50% 50%,
                rgba(0, 206, 209, 0.1) 0deg,
                rgba(0, 206, 209, 0.1) 2deg,
                transparent 2deg,
                transparent 8deg,
                rgba(135, 206, 235, 0.08) 8deg,
                rgba(135, 206, 235, 0.08) 10deg,
                transparent 10deg,
                transparent 16deg
              )
            `,
            backgroundSize: '8px 8px, 8px 8px, 24px 24px, 24px 24px, 6px 6px, 6px 6px, 200px 150px, 200px 150px, 180px 120px, 360deg',
            pointerEvents: 'none'
          }}
        />



        {/* City Name Microprint Text - Back side with different positions */}
        {microprintPositions.map((position) => (
          <Typography
            key={`back-${position.id}`}
            sx={{
              position: 'absolute',
              top: `${(position.top + 20) % 80 + 10}%`, // Offset positions for back side
              left: `${(position.left + 30) % 80 + 10}%`,
              fontSize: preview ? '8px' : '6px', // Increased font size for visibility
              fontWeight: 'bold',
              color: '#0d47a1', // Darker blue for better visibility
              opacity: 0.35, // Slightly more transparent on back but still visible
              transform: `rotate(${position.rotation + 45}deg)`, // Different rotation
              transformOrigin: 'center',
              pointerEvents: 'none',
              zIndex: 3, // Above pattern but below main content
              fontFamily: 'monospace',
              letterSpacing: '0.3px',
              userSelect: 'none',
              textShadow: '0 0 1px rgba(255, 255, 255, 0.4)', // Add subtle shadow for contrast
              WebkitTextStroke: '0.2px rgba(13, 71, 161, 0.7)' // Add text stroke for definition
            }}
          >
            {position.text}
          </Typography>
        ))}

        {/* Subcity Pattern Image Overlay - Back side only on main content and footer, excluding header */}
        {(idCard.subcity_admin_approved || currentTenantType === 'subcity') && subcityPatternImage && (
          <Box
            sx={{
              position: 'absolute',
              top: preview ? 140 : 110, // Below header completely
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `url(${subcityPatternImage})`,
              backgroundSize: 'cover', // Cover the entire area
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'center',
              opacity: 0.03, // Even lighter for back side
              zIndex: 1, // Behind all content
              pointerEvents: 'none'
            }}
          />
        )}

        

        {/* Content */}
        <Box sx={{
          p: preview ? 1.5 : 1,
          pb: preview ? 3 : 2, // Extra bottom padding for footer space
          display: 'flex',
          gap: preview ? 1.5 : 1,
          position: 'relative',
          zIndex: 2,
          alignItems: 'center', // Vertically center align the content
          minHeight: 'calc(100% - 60px)' // Account for padding and footer
        }}>
          {/* Left side - Additional info */}
          <Box sx={{ flex: 1 }}>
            <Typography
              sx={{
                fontSize: preview ? '12px' : '10px',
                mb: 1,
                fontWeight: 'bold',
                color: '#091057' // Dark blue color
              }}
            >
              ተጨማሪ መረጃ | Additional Information
            </Typography>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: preview ? 0.6 : 0.4 }}>
              <Box>
                <Typography sx={{ fontSize: preview ? '10px' : '8px', color: '#091057', fontWeight: 'bold' }}>
                  ስልክ ቁጥር | Phone Number
                </Typography>
                <Typography sx={{ fontSize: preview ? '12px' : '10px', fontWeight: 'bold', color: '#000000' }}>
                  {/* Use same field structure as CitizenDetails page */}
                  {citizen.phone || citizen.phone_number || 'Not provided'}
                </Typography>
              </Box>

              <Box>
                <Typography sx={{ fontSize: preview ? '10px' : '8px', color: '#091057', fontWeight: 'bold' }}>
                  የአደጋ ጊዜ ተጠሪ | Emergency Contact
                </Typography>
                <Typography sx={{ fontSize: preview ? '12px' : '10px', fontWeight: 'bold', color: '#000000' }}>
                  {/* Emergency contacts are in familyData.emergencyContacts, not citizen.emergency_contacts */}
                  {idCard.familyData?.emergencyContacts && idCard.familyData.emergencyContacts.length > 0
                    ? `${idCard.familyData.emergencyContacts[0].first_name || ''} ${idCard.familyData.emergencyContacts[0].middle_name || ''} ${idCard.familyData.emergencyContacts[0].last_name || ''}`.trim()
                    : citizen.emergency_contacts && citizen.emergency_contacts.length > 0
                      ? `${citizen.emergency_contacts[0].first_name || ''} ${citizen.emergency_contacts[0].middle_name || ''} ${citizen.emergency_contacts[0].last_name || ''}`.trim()
                      : 'Not provided'}
                </Typography>
                <Typography sx={{ fontSize: preview ? '12px' : '10px', color: '#000000', fontWeight: '500' }}>
                  {idCard.familyData?.emergencyContacts && idCard.familyData.emergencyContacts.length > 0
                    ? idCard.familyData.emergencyContacts[0].phone || 'Not provided'
                    : citizen.emergency_contacts && citizen.emergency_contacts.length > 0
                      ? citizen.emergency_contacts[0].phone || 'Not provided'
                      : 'Not provided'}
                </Typography>
              </Box>

              <Box>
                <Typography sx={{ fontSize: preview ? '10px' : '8px', color: '#091057', fontWeight: 'bold' }}>
                  የደም አይነት | Blood Type
                </Typography>
                <Typography sx={{ fontSize: preview ? '12px' : '10px', fontWeight: 'bold', color: '#000000' }}>
                  {citizen.blood_type || 'Not specified'}
                </Typography>
              </Box>

              <Box>
                <Typography sx={{ fontSize: preview ? '10px' : '8px', color: '#091057', fontWeight: 'bold' }}>
                  ቀጠና | Ketena
                </Typography>
                <Typography sx={{ fontSize: preview ? '12px' : '10px', fontWeight: 'bold', color: '#000000' }}>
                  {/* Use ketena_name from serializer, fallback to parsing ketena field */}
                  {citizen.ketena_name ||
                   (typeof citizen.ketena === 'object' && citizen.ketena?.name
                     ? citizen.ketena.name
                     : typeof citizen.ketena === 'string'
                       ? citizen.ketena
                       : 'Not specified')}
                </Typography>
              </Box>

              <Box>
                <Typography sx={{ fontSize: preview ? '10px' : '8px', color: '#091057', fontWeight: 'bold' }}>
                  የትውልድ ቦታ | Place of Birth
                </Typography>
                <Typography sx={{ fontSize: preview ? '12px' : '10px', fontWeight: 'bold', color: '#000000' }}>
                  {/* Use subcity as place of birth like CitizenDetails */}
                  {typeof citizen.subcity === 'object' && citizen.subcity?.name
                    ? citizen.subcity.name
                    : typeof citizen.subcity === 'string'
                      ? citizen.subcity
                      : 'Gondar'}
                </Typography>
              </Box>


            </Box>

            <Box sx={{ mt: preview ? 1.5 : 1, pt: preview ? 1 : 0.5, borderTop: '1px solid #e9ecef', textAlign: 'center' }}>
              <Typography
                sx={{
                  fontSize: preview ? '11px' : '9px',
                  fontStyle: 'italic',
                  color: '#7f8c8d',
                  lineHeight: 1.3,
                  textAlign: 'center'
                }}
              >
                ይህንን ካርድ ጠፍቶ ካገኙት ለተቋሙ ወይም በአቅራቢያዎ ወደሚገኝ ፖሊስ ጣቢያ ያስረክቡ።<br/>
                If found, please return to the issuing office or the nearest police station.
              </Typography>
            </Box>
          </Box>

          {/* Right side - QR Code */}
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            mt: preview ? -4 : -3 // Move QR code and text up more
          }}>
            <Typography
              sx={{
                fontSize: preview ? '9px' : '7px',
                mb: 0.5,
                fontWeight: 'bold',
                color: '#000000' // Dark blue color
              }}
            >
              ማረጋገጫ ኮድ | Verification Code
            </Typography>

            {/* QR Code - 15mm x 15mm (57px at 96 DPI) */}
            <Box
              sx={{
                width: preview ? 85 : 57, // 15mm = 57px at 96 DPI
                height: preview ? 85 : 57, // 15mm = 57px at 96 DPI
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#ffffff',
                mb: 0.5
              }}
            >
              {idCard.uuid || citizen.digital_id || idCard.citizen_digital_id ? (
                <QRCode
                  value={`https://goid.gov.et/verify/${idCard.uuid || citizen.digital_id || idCard.citizen_digital_id}`}
                  size={preview ? 85 : 57} // 15mm = 57px at 96 DPI
                  level="M"
                />
              ) : (
                <Typography sx={{ fontSize: preview ? '12px' : '8px', textAlign: 'center', color: '#7f8c8d', fontWeight: 'bold' }}>
                  QR<br/>CODE
                </Typography>
              )}
            </Box>

            <Typography
              sx={{
                fontSize: preview ? '6px' : '5px',
                textAlign: 'center',
                color: '#7f8c8d'
              }}
            >
              goid.gov.et/verify
            </Typography>
          </Box>
        </Box>

        {/* Footer with Issue/Expiry Dates - More transparent to show geometric pattern */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            backgroundColor: 'rgba(89, 128, 167, 0.14)', // More transparent to show pattern clearly
            borderTop: '1px solid rgba(233, 236, 239, 0.8)',
            px: preview ? 1.5 : 1,
            py: preview ? 0.4 : 0.3,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 10, // Higher z-index to ensure visibility over pattern
            backdropFilter: 'blur(0.5px)' // Minimal blur to maintain pattern visibility
          }}
        >
          <Typography
            sx={{
              fontSize: preview ? '9px' : '7px',
              color: '#2c3e50',
              fontWeight: 'bold',
              letterSpacing: '0.2px',
              textAlign: 'center'
            }}
          >
            ISSUE/የተሰጠበት: {idCard.issue_date ? new Date(idCard.issue_date).toLocaleDateString('en-GB') : new Date().toLocaleDateString('en-GB')} | EXPIRY/የሚያበቃበት: {idCard.expiry_date ? new Date(idCard.expiry_date).toLocaleDateString('en-GB') : new Date(new Date().setFullYear(new Date().getFullYear() + 10)).toLocaleDateString('en-GB')}
          </Typography>
        </Box>
      </Box>
    );
  }
};

export default IDCardTemplate;
