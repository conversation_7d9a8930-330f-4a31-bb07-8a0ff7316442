"""
Photo processing utilities for ID cards.
Includes background removal and image enhancement.
"""

import io
import base64
from PIL import Image, ImageEnhance, ImageFilter
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
import logging

logger = logging.getLogger(__name__)

try:
    from rembg import remove, new_session
    REMBG_AVAILABLE = True
    logger.info("rembg imported successfully. Background removal is available.")
except ImportError as e:
    REMBG_AVAILABLE = False
    logger.warning(f"rembg not available: {e}. Background removal will be disabled.")
except Exception as e:
    REMBG_AVAILABLE = False
    logger.warning(f"rembg import failed: {e}. Background removal will be disabled.")

try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
    logger.info("OpenCV imported successfully. Advanced image processing is available.")
except ImportError as e:
    CV2_AVAILABLE = False
    logger.warning(f"opencv-python not available: {e}. Advanced image processing will be disabled.")
except Exception as e:
    CV2_AVAILABLE = False
    logger.warning(f"OpenCV import failed: {e}. Advanced image processing will be disabled.")


class PhotoProcessor:
    """
    Photo processing class for ID card photos.
    """
    
    def __init__(self):
        self.session = None
        if REMBG_AVAILABLE:
            try:
                # Use u2net model for better person detection
                self.session = new_session('u2net')
            except Exception as e:
                logger.error(f"Failed to initialize rembg session: {e}")
                self.session = None
    
    def remove_background(self, image_data):
        """
        Remove background from image using rembg or fallback to basic processing.

        Args:
            image_data: PIL Image or bytes

        Returns:
            PIL Image with transparent background or enhanced image
        """
        # Convert to PIL Image if needed
        if isinstance(image_data, Image.Image):
            image = image_data
        else:
            image = Image.open(io.BytesIO(image_data))

        if not REMBG_AVAILABLE or not self.session:
            logger.info("Background removal not available, applying basic enhancement instead")
            # Fallback: Apply basic enhancement without background removal
            return self._apply_basic_enhancement(image)

        try:
            # Convert to bytes if PIL Image
            if isinstance(image_data, Image.Image):
                img_bytes = io.BytesIO()
                image_data.save(img_bytes, format='PNG')
                img_bytes = img_bytes.getvalue()
            else:
                img_bytes = image_data

            # Remove background
            output = remove(img_bytes, session=self.session)

            # Convert back to PIL Image
            return Image.open(io.BytesIO(output))

        except Exception as e:
            logger.error(f"Background removal failed: {e}")
            # Fallback to basic enhancement
            return self._apply_basic_enhancement(image)

    def _apply_basic_enhancement(self, image):
        """
        Apply basic image enhancement without background removal.

        Args:
            image: PIL Image

        Returns:
            Enhanced PIL Image
        """
        try:
            # Convert to RGB if necessary
            if image.mode in ('RGBA', 'LA'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'RGBA':
                    background.paste(image, mask=image.split()[-1])
                else:
                    background.paste(image, mask=image.split()[-1])
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')

            # Apply basic enhancements
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.2)

            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.1)

            enhancer = ImageEnhance.Color(image)
            image = enhancer.enhance(1.05)

            return image

        except Exception as e:
            logger.error(f"Basic enhancement failed: {e}")
            return image
    
    def enhance_photo(self, image, target_size=(400, 500)):
        """
        Enhance photo for ID card use.
        
        Args:
            image: PIL Image
            target_size: tuple (width, height)
            
        Returns:
            Enhanced PIL Image
        """
        try:
            # Convert to RGB if necessary
            if image.mode in ('RGBA', 'LA'):
                # Create white background for transparent images
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'RGBA':
                    background.paste(image, mask=image.split()[-1])
                else:
                    background.paste(image, mask=image.split()[-1])
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Resize to target size while maintaining aspect ratio
            image.thumbnail(target_size, Image.Resampling.LANCZOS)
            
            # Create final image with exact target size
            final_image = Image.new('RGB', target_size, (255, 255, 255))
            
            # Center the image
            x = (target_size[0] - image.width) // 2
            y = (target_size[1] - image.height) // 2
            final_image.paste(image, (x, y))
            
            # Enhance image quality
            enhancer = ImageEnhance.Sharpness(final_image)
            final_image = enhancer.enhance(1.2)
            
            enhancer = ImageEnhance.Contrast(final_image)
            final_image = enhancer.enhance(1.1)
            
            return final_image
            
        except Exception as e:
            logger.error(f"Photo enhancement failed: {e}")
            return image
    
    def process_id_photo(self, image_data, remove_bg=True, enhance=True):
        """
        Complete photo processing pipeline for ID cards.
        
        Args:
            image_data: PIL Image or bytes
            remove_bg: bool, whether to remove background
            enhance: bool, whether to enhance the photo
            
        Returns:
            dict with processed image data
        """
        try:
            # Load image
            if isinstance(image_data, Image.Image):
                image = image_data
            else:
                image = Image.open(io.BytesIO(image_data))
            
            original_image = image.copy()
            processed_image = image.copy()
            
            # Remove background if requested and available
            background_removed = False
            if remove_bg:
                if REMBG_AVAILABLE and self.session:
                    processed_image = self.remove_background(processed_image)
                    background_removed = True
                else:
                    # Apply basic enhancement instead
                    processed_image = self._apply_basic_enhancement(processed_image)
                    logger.info("Applied basic enhancement instead of background removal")
            
            # Enhance photo if requested
            if enhance:
                processed_image = self.enhance_photo(processed_image)
            
            # Convert to bytes
            output_buffer = io.BytesIO()
            processed_image.save(output_buffer, format='PNG', quality=95)
            processed_bytes = output_buffer.getvalue()
            
            # Convert to base64 for frontend
            processed_base64 = base64.b64encode(processed_bytes).decode('utf-8')
            
            return {
                'success': True,
                'processed_image': processed_base64,
                'format': 'PNG',
                'size': processed_image.size,
                'background_removed': background_removed,
                'enhanced': enhance,
                'rembg_available': REMBG_AVAILABLE
            }
            
        except Exception as e:
            logger.error(f"Photo processing failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'background_removed': False,
                'enhanced': False,
                'rembg_available': REMBG_AVAILABLE
            }
    
    def create_id_card_photo(self, image_data, style='professional'):
        """
        Create ID card optimized photo with specific styling.
        
        Args:
            image_data: PIL Image or bytes
            style: str, photo style ('professional', 'passport', 'license')
            
        Returns:
            dict with styled photo data
        """
        try:
            # Process with background removal and enhancement
            result = self.process_id_photo(image_data, remove_bg=True, enhance=True)
            
            if not result['success']:
                return result
            
            # Decode the processed image
            processed_bytes = base64.b64decode(result['processed_image'])
            image = Image.open(io.BytesIO(processed_bytes))
            
            # Apply style-specific processing
            if style == 'professional':
                # Add subtle shadow for depth
                image = self._add_professional_styling(image)
            elif style == 'passport':
                # Standard passport photo styling
                image = self._add_passport_styling(image)
            elif style == 'license':
                # Driver's license style
                image = self._add_license_styling(image)
            
            # Convert final result
            output_buffer = io.BytesIO()
            image.save(output_buffer, format='PNG', quality=95)
            final_bytes = output_buffer.getvalue()
            final_base64 = base64.b64encode(final_bytes).decode('utf-8')
            
            result['processed_image'] = final_base64
            result['style'] = style
            
            return result
            
        except Exception as e:
            logger.error(f"ID card photo creation failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'style': style
            }
    
    def _add_professional_styling(self, image):
        """Add professional styling to the photo."""
        # Add subtle shadow and enhance contrast
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.15)
        
        enhancer = ImageEnhance.Color(image)
        image = enhancer.enhance(1.1)
        
        return image
    
    def _add_passport_styling(self, image):
        """Add passport photo styling."""
        # Standard passport photo processing
        enhancer = ImageEnhance.Sharpness(image)
        image = enhancer.enhance(1.1)
        
        return image
    
    def _add_license_styling(self, image):
        """Add driver's license styling."""
        # License photo styling with slight desaturation
        enhancer = ImageEnhance.Color(image)
        image = enhancer.enhance(0.95)
        
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.05)
        
        return image


# Global processor instance
photo_processor = PhotoProcessor()
